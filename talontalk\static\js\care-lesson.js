/**
 * C.A.R.E. Framework Interactive Lesson System
 * Handles phase navigation, progress tracking, and AI tutor interaction
 */

class CARELessonManager {
    constructor() {
        this.currentPhase = 'contextualize';
        this.phaseProgress = 0;
        this.totalPhases = 4;
        this.phases = ['contextualize', 'acquire', 'reinforce', 'extend'];

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadPhase('contextualize');
        this.updateProgressBar();
    }

    setupEventListeners() {
        // Phase navigation buttons
        document.querySelectorAll('.care-nav-item').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const phase = e.target.dataset.phase || e.target.closest('.care-nav-item').dataset.phase;
                if (phase) {
                    this.loadPhase(phase);
                }
            });
        });

        // Phase indicators (also clickable)
        document.querySelectorAll('.care-phase-indicator').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const phase = e.target.dataset.phase || e.target.closest('.care-phase-indicator').dataset.phase;
                if (phase) {
                    this.loadPhase(phase);
                }
            });
        });

        // Next phase buttons
        document.querySelectorAll('.next-phase-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.nextPhase();
            });
        });

        // AI Tutor
        const aiTutorBtn = document.getElementById('aiTutorBtn');
        const closeTutorBtn = document.getElementById('closeTutorBtn');
        const sendTutorBtn = document.getElementById('sendTutorBtn');
        const tutorInput = document.getElementById('tutorInput');

        if (aiTutorBtn) {
            aiTutorBtn.addEventListener('click', () => this.openAITutor());
        }

        if (closeTutorBtn) {
            closeTutorBtn.addEventListener('click', () => this.closeAITutor());
        }

        if (sendTutorBtn) {
            sendTutorBtn.addEventListener('click', () => this.sendTutorMessage());
        }

        if (tutorInput) {
            tutorInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendTutorMessage();
                }
            });
        }
    }

    loadPhase(phaseName) {
        console.log(`Loading phase: ${phaseName}`);

        // Hide all phases
        document.querySelectorAll('.care-phase-content').forEach(content => {
            content.classList.add('hidden');
        });

        // Show target phase
        const phaseContent = document.getElementById(`${phaseName}Phase`);
        if (phaseContent) {
            phaseContent.classList.remove('hidden');
            phaseContent.classList.add('care-phase-enter');

            // Trigger animation
            setTimeout(() => {
                phaseContent.classList.remove('care-phase-enter');
                phaseContent.classList.add('care-phase-active');
            }, 50);
        }

        // Update phase indicators
        this.updatePhaseIndicators(phaseName);

        // Update current phase
        this.currentPhase = phaseName;
        this.phaseProgress = this.phases.indexOf(phaseName) + 1;
        this.updateProgressBar();

        // Load phase-specific content
        this.loadPhaseContent(phaseName);
    }

    loadPhaseContent(phaseName) {
        console.log(`🔄 Loading content for phase: ${phaseName}`);

        // Make API call to get phase-specific content
        fetch(`/care/api/phase/${phaseName}/`, {
            method: 'GET',
            headers: {
                'X-CSRFToken': this.getCSRFToken(),
                'Content-Type': 'application/json',
            },
        })
            .then(response => {
                console.log(`📡 API Response for ${phaseName}:`, response.status);
                return response.json();
            })
            .then(data => {
                console.log(`📊 Data received for ${phaseName}:`, data);

                if (data.success) {
                    console.log(`✅ Rendering content for ${phaseName}`);
                    this.renderPhaseContent(phaseName, data.content);
                } else {
                    console.error('Failed to load phase content:', data.error);
                    this.showFallbackContent(phaseName);
                }
            })
            .catch(error => {
                console.error(`❌ Error loading ${phaseName} content:`, error);
                this.showFallbackContent(phaseName);
            });
    }

    renderPhaseContent(phaseName, content) {
        console.log(`🎨 Rendering ${phaseName} with content:`, content);

        const phaseContainer = document.getElementById(`${phaseName}Phase`);
        if (!phaseContainer) {
            console.error(`❌ Phase container not found: ${phaseName}Phase`);
            return;
        }

        // Basic content rendering - can be enhanced based on content type
        const contentHTML = this.generatePhaseHTML(phaseName, content);
        console.log(`🔧 Generated HTML length for ${phaseName}:`, contentHTML.length);

        phaseContainer.innerHTML = contentHTML;

        // Reinitialize event listeners for new content
        this.setupPhaseSpecificListeners(phaseName);

        console.log(`✅ Phase ${phaseName} rendered successfully`);
    }

    generatePhaseHTML(phaseName, content) {
        switch (phaseName) {
            case 'contextualize':
                return this.generateContextualizeHTML(content);
            case 'acquire':
                return this.generateAcquireHTML(content);
            case 'reinforce':
                return this.generateReinforceHTML(content);
            case 'extend':
                return this.generateExtendHTML(content);
            default:
                return '<p>Phase content loading...</p>';
        }
    } generateContextualizeHTML(content) {
        const scenario = content?.scenario || {};
        const culturalContext = content?.cultural_context || {};
        const keyPhrases = content?.key_phrases || [];

        return `
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white p-6">
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                            <i class="fas fa-globe-americas text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold">Contextualize</h2>
                            <p class="text-emerald-100">Set the scene for your learning journey</p>
                        </div>
                    </div>
                </div>
                
                <!-- Content -->
                <div class="p-6 space-y-6">
                    <!-- Scenario Card -->
                    <div class="bg-gray-50 rounded-xl p-6">
                        <div class="flex items-start gap-3 mb-4">
                            <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-map-marker-alt text-emerald-600"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">${scenario.title || 'Real-World Scenario'}</h3>
                                <p class="text-gray-700 leading-relaxed">
                                    ${scenario.description || "Imagine you're visiting a café in Madrid during a busy morning. You want to order coffee and a pastry, but you need to know the right phrases and cultural context."}
                                </p>
                                ${scenario.location ? `<div class="mt-3 flex items-center gap-2 text-sm text-gray-600">
                                    <i class="fas fa-location-dot"></i>
                                    <span>${scenario.location}</span>
                                </div>` : ''}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Two Column Layout -->
                    <div class="grid md:grid-cols-2 gap-6">
                        <!-- Cultural Context -->
                        <div class="bg-blue-50 rounded-xl p-6">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-users text-blue-600"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-900">${culturalContext.title || 'Cultural Context'}</h4>
                            </div>
                            <ul class="space-y-3">
                                ${culturalContext.facts ? culturalContext.facts.map(fact => `
                                    <li class="flex items-start gap-3 text-gray-700">
                                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                                        <span>${fact}</span>
                                    </li>
                                `).join('') : `
                                    <li class="flex items-start gap-3 text-gray-700">
                                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                                        <span>Spanish cafés open early (around 6 AM)</span>
                                    </li>
                                    <li class="flex items-start gap-3 text-gray-700">
                                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                                        <span>Locals often stand at the bar for coffee</span>
                                    </li>
                                    <li class="flex items-start gap-3 text-gray-700">
                                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                                        <span>Tipping is appreciated but not mandatory</span>
                                    </li>
                                `}
                            </ul>
                        </div>
                        
                        <!-- Key Phrases -->
                        <div class="bg-purple-50 rounded-xl p-6">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-comment text-purple-600"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-900">Key Phrases</h4>
                            </div>
                            <div class="space-y-4">
                                ${keyPhrases.slice(0, 3).map(phrase => `
                                    <div class="bg-white rounded-lg p-4 border border-purple-100">
                                        <div class="text-lg font-semibold text-gray-900 mb-1">${phrase.spanish}</div>
                                        <div class="text-gray-600 mb-2">${phrase.english}</div>
                                        <div class="text-sm text-purple-600 font-mono">[${phrase.pronunciation}]</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Button -->
                <div class="p-6 bg-gray-50 border-t border-gray-100">
                    <div class="flex justify-center">
                        <button class="next-phase-btn bg-emerald-600 hover:bg-emerald-700 text-white px-8 py-3 rounded-xl font-semibold text-lg transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all">
                            Ready to Learn Vocabulary →
                        </button>
                    </div>
                </div>
            </div>
        `;
    } generateAcquireHTML(content) {
        const vocabulary = content?.vocabulary || [];
        const grammar = content?.grammar || {};

        return `
            <div class="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                <!-- Header -->
                <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6">
                    <div class="flex items-center gap-3 mb-2">
                        <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                            <i class="fas fa-lightbulb text-lg"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold">Acquire</h2>
                            <p class="text-blue-100">Learn essential vocabulary and phrases</p>
                        </div>
                    </div>
                </div>
                
                <!-- Content -->
                <div class="p-6 space-y-6">
                    <!-- Two Column Layout -->
                    <div class="grid md:grid-cols-2 gap-6">
                        <!-- Vocabulary Section -->
                        <div class="space-y-4">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-book text-blue-600"></i>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-900">Essential Vocabulary</h3>
                            </div>
                            <div class="space-y-3">
                                ${vocabulary.map(item => `
                                    <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                                        <div class="flex justify-between items-start mb-2">
                                            <div class="text-lg font-bold text-gray-900">${item.word}</div>
                                            <div class="text-sm text-blue-600 font-medium">${item.translation}</div>
                                        </div>
                                        <div class="text-sm text-gray-600 font-mono mb-2">[${item.pronunciation}]</div>
                                        <div class="border-t border-gray-200 pt-2 mt-2">
                                            <div class="text-sm font-medium text-gray-700">${item.example}</div>
                                            <div class="text-sm text-gray-500">${item.example_translation}</div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        
                        <!-- Grammar Section -->
                        <div class="space-y-4">
                            <div class="flex items-center gap-3 mb-4">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-cogs text-green-600"></i>
                                </div>
                                <h3 class="text-xl font-semibold text-gray-900">${grammar.topic || 'Grammar Patterns'}</h3>
                            </div>
                            <div class="space-y-4">
                                ${grammar.structures ? grammar.structures.map(structure => `
                                    <div class="bg-green-50 rounded-lg p-4 border border-green-100">
                                        <div class="text-lg font-bold text-gray-900 mb-2">${structure.pattern}</div>
                                        <div class="text-gray-600 mb-3">${structure.meaning}</div>
                                        <div class="space-y-2">
                                            ${structure.examples.map(example => `
                                                <div class="bg-white rounded p-3 text-sm border border-green-200">
                                                    ${example}
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                `).join('') : `
                                    <div class="bg-green-50 rounded-lg p-4 border border-green-100">
                                        <div class="text-lg font-bold text-gray-900 mb-2">Basic Greetings</div>
                                        <div class="text-gray-600 mb-3">Essential phrases for polite conversation</div>
                                        <div class="bg-white rounded p-3 text-sm border border-green-200">
                                            "Buenos días" - Good morning
                                        </div>
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Button -->
                <div class="p-6 bg-gray-50 border-t border-gray-100">
                    <div class="flex justify-center">
                        <button class="next-phase-btn bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-xl font-semibold text-lg transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all">
                            Practice What You Learned →
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    generateReinforceHTML(content) {
        const exercises = content?.exercises || [];

        return `
            <div class="reinforce-theme text-white p-8 rounded-2xl shadow-xl">
                <div class="text-center mb-8">
                    <h2 class="text-4xl font-bold mb-4">💪 Reinforce</h2>
                    <p class="text-xl opacity-90">Practice and strengthen your knowledge</p>
                </div>
                
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 mb-6">
                    <h3 class="text-2xl font-semibold mb-4">Interactive Practice</h3>
                    <div id="practiceArea" class="space-y-6">
                        ${exercises.map((exercise, index) => {
            if (exercise.type === 'multiple_choice') {
                return `
                                    <div class="practice-question bg-white/20 rounded-lg p-4">
                                        <p class="text-lg mb-3">${exercise.question}</p>
                                        <div class="grid grid-cols-1 gap-2">
                                            ${exercise.options.map((option, optIndex) => `
                                                <button class="practice-option bg-white/20 hover:bg-white/30 p-3 rounded-lg transition-colors text-left" 
                                                        data-answer="${optIndex === exercise.correct ? 'correct' : 'wrong'}"
                                                        data-explanation="${exercise.explanation}">
                                                    ${option}
                                                </button>
                                            `).join('')}
                                        </div>
                                    </div>
                                `;
            } else if (exercise.type === 'translation') {
                return `
                                    <div class="practice-question bg-white/20 rounded-lg p-4">
                                        <p class="text-lg mb-3">${exercise.question}</p>
                                        <input type="text" class="translation-input w-full p-3 rounded-lg bg-white/20 text-white placeholder-gray-300" 
                                               placeholder="Type your translation here..."
                                               data-answer="${exercise.answer}"
                                               data-explanation="${exercise.explanation}">
                                        <button class="check-translation mt-2 bg-white/30 hover:bg-white/40 px-4 py-2 rounded-lg">Check Answer</button>
                                    </div>
                                `;
            } else if (exercise.type === 'pronunciation') {
                return `
                                    <div class="practice-question bg-white/20 rounded-lg p-4">
                                        <p class="text-lg mb-2">Practice pronunciation:</p>
                                        <div class="text-center">
                                            <p class="text-2xl font-bold mb-2">${exercise.phrase}</p>
                                            <p class="text-lg opacity-80 mb-2">${exercise.translation}</p>
                                            <p class="text-sm opacity-60 mb-4">[${exercise.phonetic}]</p>
                                            <button class="pronunciation-btn bg-white/30 hover:bg-white/40 px-6 py-3 rounded-lg">
                                                🔊 Listen & Practice
                                            </button>
                                        </div>
                                    </div>
                                `;
            }
            return '';
        }).join('')}
                    </div>
                </div>
                
                <div class="text-center">
                    <button class="next-phase-btn bg-white text-purple-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-colors shadow-lg">
                        Apply in Real Conversations →
                    </button>
                </div>
            </div>
        `;
    }

    generateExtendHTML(content) {
        const realWorldApps = content?.real_world_applications || [];
        const expansionTopics = content?.expansion_topics || [];
        const homework = content?.homework || {};

        return `
            <div class="extend-theme text-white p-8 rounded-2xl shadow-xl">
                <div class="text-center mb-8">
                    <h2 class="text-4xl font-bold mb-4">🚀 Extend</h2>
                    <p class="text-xl opacity-90">Apply your knowledge in real-world situations</p>
                </div>
                
                <div class="grid md:grid-cols-2 gap-6 mb-8">
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                        <h3 class="text-2xl font-semibold mb-4">Real-World Practice</h3>
                        <div class="space-y-4">
                            ${realWorldApps.map(app => `
                                <div class="bg-white/20 rounded-lg p-4">
                                    <h4 class="font-bold text-lg mb-2">${app.title}</h4>
                                    <p class="text-sm opacity-90 mb-3">${app.description}</p>
                                    <div class="text-xs opacity-80 mb-2">Scenario: ${app.scenario}</div>
                                    <div class="space-y-1">
                                        ${app.tasks ? app.tasks.map(task => `
                                            <div class="text-xs bg-white/10 rounded px-2 py-1">• ${task}</div>
                                        `).join('') : ''}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                        <h3 class="text-2xl font-semibold mb-4">Expand Your Knowledge</h3>
                        <div class="space-y-4">
                            ${expansionTopics.map(topic => `
                                <div class="bg-white/20 rounded-lg p-4">
                                    <h4 class="font-bold mb-2">${topic.topic}</h4>
                                    <div class="text-sm space-y-2">
                                        <div>
                                            <strong>Vocabulary:</strong> ${topic.vocabulary.join(', ')}
                                        </div>
                                        <div>
                                            <strong>Phrases:</strong>
                                            ${topic.phrases.map(phrase => `<div class="ml-2">• ${phrase}</div>`).join('')}
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                
                ${homework.title ? `
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 mb-6">
                        <h3 class="text-2xl font-semibold mb-4">${homework.title}</h3>
                        <p class="mb-4">${homework.description}</p>
                        <div class="space-y-2">
                            ${homework.steps ? homework.steps.map((step, index) => `
                                <div class="flex items-start space-x-2">
                                    <span class="bg-white/20 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">${index + 1}</span>
                                    <span>${step}</span>
                                </div>
                            `).join('') : ''}
                        </div>
                    </div>
                ` : ''}
                
                <div class="text-center">
                    <button class="next-phase-btn bg-white text-amber-600 px-8 py-4 rounded-xl font-bold text-lg hover:bg-gray-100 transition-colors shadow-lg">
                        Complete Lesson ✓
                    </button>
                </div>
            </div>
        `;
    }

    showFallbackContent(phaseName) {
        const phaseContainer = document.getElementById(`${phaseName}Phase`);
        if (!phaseContainer) return;

        phaseContainer.innerHTML = `
            <div class="text-center py-12">
                <div class="text-6xl mb-4">📚</div>
                <h3 class="text-2xl font-bold mb-4">Loading ${phaseName.charAt(0).toUpperCase() + phaseName.slice(1)} Content</h3>
                <p class="text-gray-600 mb-6">We're preparing your learning experience...</p>
                <button class="next-phase-btn bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    Continue to Next Phase →
                </button>
            </div>
        `;

        this.setupPhaseSpecificListeners(phaseName);
    }

    setupPhaseSpecificListeners(phaseName) {
        console.log(`🔧 Setting up listeners for ${phaseName} phase`);

        // Setup listeners for Next Phase buttons (most important fix!)
        document.querySelectorAll('.next-phase-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                console.log('📤 Next phase button clicked');
                e.preventDefault();
                this.nextPhase();
            });
        });

        // Setup listeners for practice questions
        document.querySelectorAll('.practice-option').forEach(btn => {
            btn.addEventListener('click', (e) => {
                console.log('🎯 Practice option clicked');
                this.handlePracticeAnswer(e.target);
            });
        });

        // Setup listeners for conversation responses  
        document.querySelectorAll('.response-option').forEach(btn => {
            btn.addEventListener('click', (e) => {
                console.log('💬 Response option clicked');
                this.handleConversationResponse(e.target);
            });
        });

        // Setup listeners for translation checks
        document.querySelectorAll('.check-translation').forEach(btn => {
            btn.addEventListener('click', (e) => {
                console.log('✅ Check translation clicked');
                this.handleTranslationCheck(e.target);
            });
        });

        // Setup listeners for pronunciation practice
        document.querySelectorAll('.pronunciation-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                console.log('🔊 Pronunciation button clicked');
                this.handlePronunciationPractice(e.target);
            });
        });

        console.log(`✅ Listeners set up for ${phaseName}`);
    }

    handlePracticeAnswer(button) {
        const isCorrect = button.dataset.answer === 'correct';

        // Visual feedback
        if (isCorrect) {
            button.classList.add('bg-green-500');
            button.innerHTML += ' ✓';
        } else {
            button.classList.add('bg-red-500');
            button.innerHTML += ' ✗';
        }

        // Disable all options
        document.querySelectorAll('.practice-option').forEach(btn => {
            btn.disabled = true;
            btn.classList.add('opacity-75');
        });

        // Show feedback
        setTimeout(() => {
            alert(isCorrect ? 'Correct! Well done!' : 'Not quite right. The correct answer is highlighted.');
        }, 500);
    }

    handleConversationResponse(button) {
        // Add user response to conversation
        const conversationArea = document.getElementById('conversationArea');
        const userBubble = document.createElement('div');
        userBubble.className = 'conversation-bubble bg-blue-500/80 rounded-lg p-4 ml-8';
        userBubble.innerHTML = `
            <div class="font-semibold mb-2">You:</div>
            <p>${button.textContent}</p>
        `;

        conversationArea.appendChild(userBubble);

        // Hide response options
        document.querySelector('.response-options').style.display = 'none';

        // Show barista response
        setTimeout(() => {
            const baristaBubble = document.createElement('div');
            baristaBubble.className = 'conversation-bubble bg-white/20 rounded-lg p-4';
            baristaBubble.innerHTML = `
                <div class="font-semibold mb-2">Barista:</div>
                <p>"Perfecto. Son 3.50 euros."</p>
                <p class="text-sm opacity-75 mt-1">(Perfect. That's 3.50 euros.)</p>
            `;
            conversationArea.appendChild(baristaBubble);
        }, 1000);
    }

    nextPhase() {
        const currentIndex = this.phases.indexOf(this.currentPhase);
        if (currentIndex < this.phases.length - 1) {
            const nextPhase = this.phases[currentIndex + 1];
            this.loadPhase(nextPhase);
        } else {
            // Lesson complete
            this.completeLesson();
        }
    }

    completeLesson() {
        // Show lesson completion
        document.querySelector('.max-w-7xl').innerHTML = `
            <div class="text-center py-20">
                <div class="text-8xl mb-6">🎉</div>
                <h2 class="text-4xl font-bold text-gray-900 mb-4">Lesson Complete!</h2>
                <p class="text-xl text-gray-600 mb-8">You've successfully completed the C.A.R.E. learning experience.</p>
                <div class="space-x-4">
                    <a href="/dashboard/" class="bg-blue-600 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-blue-700 transition-colors">
                        Back to Dashboard
                    </a>
                    <button class="bg-green-600 text-white px-8 py-4 rounded-xl font-bold text-lg hover:bg-green-700 transition-colors">
                        Take Another Lesson
                    </button>
                </div>
            </div>
        `;
    }

    updatePhaseIndicators(currentPhase) {
        document.querySelectorAll('.care-phase-indicator').forEach(indicator => {
            const phase = indicator.dataset.phase;
            const circle = indicator.querySelector('div');

            if (phase === currentPhase) {
                circle.classList.add('ring-4', 'ring-white', 'ring-opacity-50');
            } else {
                circle.classList.remove('ring-4', 'ring-white', 'ring-opacity-50');
            }
        });
    }

    updateProgressBar() {
        const progressBar = document.getElementById('careProgressBar');
        if (progressBar) {
            const percentage = (this.phaseProgress / this.totalPhases) * 100;
            progressBar.style.width = `${percentage}%`;
        }
    }

    openAITutor() {
        document.getElementById('aiTutorModal').classList.remove('hidden');
    }

    closeAITutor() {
        document.getElementById('aiTutorModal').classList.add('hidden');
    }

    sendTutorMessage() {
        const input = document.getElementById('tutorInput');
        const message = input.value.trim();

        if (!message) return;

        // Add user message to chat
        const chatArea = document.getElementById('tutorChatArea');
        const userMessage = document.createElement('div');
        userMessage.className = 'user-message bg-blue-600 text-white p-4 rounded-lg mb-4 ml-8';
        userMessage.innerHTML = `<p>${message}</p>`;
        chatArea.appendChild(userMessage);

        // Clear input
        input.value = '';

        // Simulate AI response
        setTimeout(() => {
            const aiMessage = document.createElement('div');
            aiMessage.className = 'tutor-message bg-blue-50 p-4 rounded-lg mb-4';
            aiMessage.innerHTML = `<p>That's a great question! ${this.generateTutorResponse(message)}</p>`;
            chatArea.appendChild(aiMessage);

            // Scroll to bottom
            chatArea.scrollTop = chatArea.scrollHeight;
        }, 1000);
    }

    generateTutorResponse(userMessage) {
        // Simple response generation - can be enhanced with actual AI
        const responses = [
            "Let me explain that concept in more detail...",
            "That's related to what we learned in the previous phase.",
            "Here's a helpful tip for remembering that phrase...",
            "In Spanish culture, this is particularly important because...",
            "Let me give you another example to clarify..."
        ];

        return responses[Math.floor(Math.random() * responses.length)];
    }

    getCSRFToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }
}

// Initialize the C.A.R.E. lesson manager when the page loads
document.addEventListener('DOMContentLoaded', function () {
    console.log('Initializing C.A.R.E. Lesson Manager...');
    window.careManager = new CARELessonManager();
});

from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user_model
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.contrib import messages
from django.views.decorators.http import require_http_methods
import json
import logging
from lessons.models import Lesson, UserLessonProgress
from gamification.models import Achievement, Streak, Level
from profiles.models import Profile

logger = logging.getLogger(__name__)


def landing_view(request):
    """Landing page view - redirect to dashboard if authenticated"""
    if request.user.is_authenticated:
        return redirect("dashboard")
    return render(request, "landing.html")


@login_required
def dashboard_view(request):
    """User dashboard showing progress, lessons, and gamification data"""
    user = request.user

    # Get or create user profile
    profile, created = Profile.objects.get_or_create(
        user=user,
        defaults={
            "native_language": "English",
            "target_language": "Spanish",
            "xp": 0,
            "streak": 0,
        },
    )

    # Redirect to onboarding if not completed
    if not profile.onboarding_completed:
        return redirect("onboarding")

    # Get or create streak data
    streak, created = Streak.objects.get_or_create(
        user=user, defaults={"current_streak": 0, "longest_streak": 0}
    )

    # Get or create level data
    level, created = Level.objects.get_or_create(
        user=user, defaults={"level": 1, "xp": 0}
    )

    # Get lessons and progress
    lessons = Lesson.objects.filter(is_active=True).order_by("order")[
        :5
    ]  # First 5 lessons
    completed_lessons = UserLessonProgress.objects.filter(
        user=user, completed=True
    ).count()

    # Get completed lesson IDs for template
    completed_lesson_ids = list(
        UserLessonProgress.objects.filter(user=user, completed=True).values_list(
            "lesson_id", flat=True
        )
    )

    # Get user achievements
    achievements = Achievement.objects.filter(user=user).select_related("badge")[:3]

    # Calculate progress percentage for current level
    xp_for_next_level = level.level * 100  # Simple formula: level * 100 XP needed
    progress_percentage = (
        min((level.xp / xp_for_next_level) * 100, 100) if xp_for_next_level > 0 else 0
    )

    # Calculate XP needed for next level
    xp_to_next_level = max(0, xp_for_next_level - level.xp)

    # Calculate words learned and minutes studied
    words_learned = completed_lessons * 8
    minutes_studied = completed_lessons * 15

    context = {
        "user": user,
        "profile": profile,
        "streak": streak,
        "level": level,
        "lessons": lessons,
        "completed_lessons": completed_lessons,
        "completed_lesson_ids": completed_lesson_ids,
        "achievements": achievements,
        "progress_percentage": progress_percentage,
        "xp_for_next_level": xp_for_next_level,
        "xp_to_next_level": xp_to_next_level,
        "words_learned": words_learned,
        "minutes_studied": minutes_studied,
    }

    return render(request, "dashboard.html", context)


@login_required
def flashcard_practice_view(request):
    """Dedicated flashcard practice page - immersive learning experience"""
    try:
        profile = Profile.objects.get(user=request.user)
    except Profile.DoesNotExist:
        # Redirect to onboarding if profile doesn't exist
        return redirect("onboarding")

    context = {
        "profile": profile,
        "user": request.user,
    }

    return render(request, "flashcard_practice.html", context)


@login_required
def lesson_detail_view(request, lesson_id):
    """View for individual lesson details"""
    from django.shortcuts import get_object_or_404

    lesson = get_object_or_404(Lesson, id=lesson_id, is_active=True)
    vocabularies = lesson.vocabularies.all()

    # Get or create progress for this lesson
    progress, created = UserLessonProgress.objects.get_or_create(
        user=request.user, lesson=lesson, defaults={"completed": False, "xp_earned": 0}
    )

    context = {
        "lesson": lesson,
        "vocabularies": vocabularies,
        "progress": progress,
    }

    return render(request, "lessons/lesson_detail.html", context)


@login_required
def onboarding_view(request):
    """Multi-step onboarding page"""
    # Check if user has already completed onboarding
    try:
        profile = Profile.objects.get(user=request.user)
        if profile.onboarding_completed:
            return redirect("dashboard")
    except Profile.DoesNotExist:
        pass  # User doesn't have a profile yet, show onboarding

    return render(request, "onboarding.html")


@login_required
@require_POST
def onboarding_complete_view(request):
    """Handle onboarding completion and save user preferences"""
    try:
        # Parse the JSON data from the request
        data = json.loads(request.body)

        # Get or create user profile
        profile, created = Profile.objects.get_or_create(
            user=request.user,
            defaults={
                "native_language": "English",
                "target_language": "Spanish",
                "xp": 0,
                "streak": 0,
            },
        )

        # Update profile with onboarding data
        profile.display_name = data.get(
            "display_name", request.user.first_name or request.user.username
        )
        profile.native_language = data.get("native_language", "English")
        profile.target_language = data.get("target_language", "Spanish")
        profile.skill_level = data.get("skill_level", "Beginner")
        profile.main_goal = data.get("main_goal", "Conversational Fluency")
        profile.onboarding_completed = True  # We'll need to add this field to the model
        profile.save()

        return JsonResponse(
            {
                "success": True,
                "message": "Onboarding completed successfully!",
                "redirect_url": "/dashboard/",
            }
        )

    except Exception as e:
        return JsonResponse({"success": False, "error": str(e)}, status=400)


@login_required
def debug_dashboard_view(request):
    """Debug version of dashboard to test data"""
    user = request.user

    # Get or create user profile
    profile, created = Profile.objects.get_or_create(
        user=user,
        defaults={
            "native_language": "English",
            "target_language": "Spanish",
            "xp": 0,
            "streak": 0,
        },
    )

    # Get or create streak data
    streak, created = Streak.objects.get_or_create(
        user=user, defaults={"current_streak": 0, "longest_streak": 0}
    )

    # Get or create level data
    level, created = Level.objects.get_or_create(
        user=user, defaults={"level": 1, "xp": 0}
    )

    # Get lessons and progress
    lessons = Lesson.objects.filter(is_active=True).order_by("order")[:5]
    completed_lessons = UserLessonProgress.objects.filter(
        user=user, completed=True
    ).count()
    completed_lesson_ids = list(
        UserLessonProgress.objects.filter(user=user, completed=True).values_list(
            "lesson_id", flat=True
        )
    )

    # Get user achievements
    achievements = Achievement.objects.filter(user=user).select_related("badge")[:3]

    context = {
        "user": user,
        "profile": profile,
        "streak": streak,
        "level": level,
        "lessons": lessons,
        "completed_lessons": completed_lessons,
        "completed_lesson_ids": completed_lesson_ids,
        "achievements": achievements,
    }

    return render(request, "debug_dashboard.html", context)


def login_success_handler(request):
    """
    Custom handler for successful login - sets flag for welcome modal
    This can be called after allauth login or used as a redirect target
    """
    try:
        if request.user.is_authenticated:
            # Set session flag to show welcome modal
            request.session["show_welcome_modal"] = True
            logger.info(
                f"Login success for user: {request.user.username} (ID: {request.user.id})"
            )

            # Get user's profile for personalization
            try:
                profile = Profile.objects.get(user=request.user)
                request.session["user_target_language"] = profile.target_language
                request.session["user_difficulty"] = getattr(
                    profile, "difficulty_level", "beginner"
                )
                logger.info(
                    f"Profile loaded for {request.user.username}: {profile.target_language}, {profile.difficulty_level}"
                )
            except Profile.DoesNotExist:
                logger.warning(
                    f"No profile found for user {request.user.username}, creating defaults"
                )
                request.session["user_target_language"] = "spanish"
                request.session["user_difficulty"] = "beginner"

                # Create a default profile for the user
                try:
                    Profile.objects.create(
                        user=request.user,
                        target_language="spanish",
                        skill_level="beginner",
                    )
                    logger.info(f"Created default profile for {request.user.username}")
                except Exception as e:
                    logger.error(
                        f"Failed to create profile for {request.user.username}: {e}"
                    )

            return redirect("dashboard")
        else:
            logger.warning("Login success handler called but user not authenticated")
            return redirect("landing")

    except Exception as e:
        logger.error(f"Error in login success handler: {e}")
        return redirect("landing")


@require_http_methods(["GET"])
def check_welcome_modal(request):
    """
    API endpoint to check if welcome modal should be shown
    """
    if not request.user.is_authenticated:
        return JsonResponse({"show_modal": False})

    # Check session flag
    show_modal = request.session.pop("show_welcome_modal", False)

    # Also check if it's the user's first login today
    last_login = request.user.last_login
    if last_login:
        from django.utils import timezone

        today = timezone.now().date()
        if last_login.date() < today:
            show_modal = True

    # Get user context for the modal
    user_context = {
        "username": request.user.username,
        "first_name": request.user.first_name,
        "target_language": request.session.get("user_target_language", "spanish"),
        "difficulty": request.session.get("user_difficulty", "beginner"),
    }

    return JsonResponse(
        {
            "show_modal": show_modal,
            "user_context": user_context,
        }
    )

/**
 * TalonTalk Content Preloading and Adaptive Learning System
 * ========================================================
 * 
 * This JavaScript module handles intelligent content preloading, adaptive learning,
 * performance tracking, and seamless offline/online learning experiences.
 * 
 * Features:
 * - Content preloading and caching
 * - Performance tracking and analytics
 * - Adaptive difficulty adjustment
 * - Offline learning support
 * - Real-time recommendations
 */

class TalonTalkAdaptiveLearning {
    constructor() {
        this.apiBase = '/api/gamification/';
        this.cache = new Map();
        this.performanceData = {
            sessionId: this.generateSessionId(),
            startTime: Date.now(),
            questions: [],
            currentStreak: 0,
            totalCorrect: 0,
            totalQuestions: 0
        };
        this.isOnline = navigator.onLine;
        this.preloadedContent = null;
        this.adaptiveParams = null;

        this.init();
    }

    async init() {
        console.log('🚀 Initializing TalonTalk Adaptive Learning System');

        // Setup event listeners
        this.setupEventListeners();

        // Load cached data
        this.loadFromLocalStorage();

        // Preload content
        await this.preloadDailyContent();

        // Setup periodic sync
        this.setupPeriodicSync();

        console.log('✅ Adaptive Learning System initialized');
    }

    setupEventListeners() {
        // Simplified for local LLM - always connected
        this.isOnline = true;

        // Page unload - save data
        window.addEventListener('beforeunload', () => {
            this.saveToLocalStorage();
            if (this.performanceData.totalQuestions > 0) {
                this.trackPerformance();
            }
        });

        // Visibility change - pause/resume
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseSession();
            } else {
                this.resumeSession();
            }
        });
    }

    async preloadDailyContent(options = {}) {
        const {
            language = 'spanish',
            difficulty = 'beginner',
            contentTypes = ['flashcard', 'lesson'],
            forceRegenerate = false
        } = options;

        try {
            console.log(`🔄 Preloading content: ${language}/${difficulty}`);

            const payload = {
                language,
                difficulty,
                content_types: contentTypes,
                force_regenerate: forceRegenerate
            };

            const response = await this.apiCall('preload/', 'POST', payload);

            if (response.success || response.status === 'success') {
                this.preloadedContent = response.content || response;
                this.adaptiveParams = response.adaptive_params;

                // Cache the content
                this.cacheContent('daily_content', this.preloadedContent);

                // Show success notification
                this.showNotification('✅ Content preloaded successfully!', 'success');

                // Update UI with recommendations
                if (response.insights) {
                    this.updateRecommendations(response.insights);
                }

                console.log('✅ Content preloaded successfully', this.preloadedContent);
                return this.preloadedContent;
            } else {
                throw new Error(response.error || 'Failed to preload content');
            }
        } catch (error) {
            console.warn('⚠️ Preloading failed, using cached/fallback content:', error);

            // Try to use cached content
            const cachedContent = this.getCachedContent('daily_content');
            if (cachedContent) {
                this.preloadedContent = cachedContent;
                this.showNotification('� Using cached content from local library', 'info');
                return cachedContent;
            }

            // Fallback to demo content
            return this.getFallbackContent(language, difficulty);
        }
    }

    async getAdaptiveContent(sessionContext = {}) {
        try {
            const params = new URLSearchParams({
                language: sessionContext.language || 'spanish',
                session_type: sessionContext.type || 'practice'
            });

            const response = await this.apiCall(`adaptive-content/?${params}`);

            if (response.success) {
                this.adaptiveParams = response.adaptive_content?.adaptive_params;

                // Update UI with adaptive recommendations
                this.updateAdaptiveUI(response.adaptive_content);

                return response.adaptive_content;
            }
        } catch (error) {
            console.warn('⚠️ Failed to get adaptive content:', error);
            return null;
        }
    }

    async trackPerformance(performanceData = null) {
        const data = performanceData || this.performanceData;

        // Calculate session metrics
        const sessionDuration = Date.now() - data.startTime;
        const accuracy = data.totalQuestions > 0 ? data.totalCorrect / data.totalQuestions : 0;
        const avgResponseTime = this.calculateAverageResponseTime(data.questions);

        const payload = {
            session_id: data.sessionId,
            question_count: data.totalQuestions,
            correct_answers: data.totalCorrect,
            time_spent_seconds: Math.floor(sessionDuration / 1000),
            response_times: data.questions.map(q => q.responseTime || 5000),
            difficulty: this.getCurrentDifficulty(),
            topics: this.getTopicsCovered(),
            hints_used: data.questions.filter(q => q.usedHint).length,
            completion_rate: this.calculateCompletionRate()
        };

        if (this.isOnline) {
            try {
                const response = await this.apiCall('track-performance/', 'POST', payload);

                if (response.success) {
                    // Update local adaptive parameters
                    if (response.recommended_difficulty) {
                        this.updateDifficulty(response.recommended_difficulty);
                    }

                    // Show learning insights
                    if (response.learning_insights) {
                        this.showLearningInsights(response.learning_insights);
                    }

                    console.log('📊 Performance tracked successfully');
                    return response;
                }
            } catch (error) {
                console.warn('⚠️ Failed to track performance online, caching for later:', error);
                this.cachePerformanceData(payload);
            }
        } else {
            // Cache for later sync
            this.cachePerformanceData(payload);
            console.log('� Performance data cached for sync');
        }
    }

    recordQuestionAttempt(question, userAnswer, isCorrect, responseTime, usedHint = false) {
        const attempt = {
            questionId: question.id,
            question: question.question,
            userAnswer,
            correctAnswer: question.correct_answer,
            isCorrect,
            responseTime,
            usedHint,
            timestamp: Date.now(),
            difficulty: question.difficulty || this.getCurrentDifficulty()
        };

        this.performanceData.questions.push(attempt);
        this.performanceData.totalQuestions++;

        if (isCorrect) {
            this.performanceData.totalCorrect++;
            this.performanceData.currentStreak++;
        } else {
            this.performanceData.currentStreak = 0;
        }

        // Real-time adaptive adjustment
        this.checkForDifficultyAdjustment();

        // Update UI
        this.updateProgressUI();

        console.log(`📝 Question attempt recorded: ${isCorrect ? '✅' : '❌'} (${responseTime}ms)`);
    }

    async getContentRecommendations() {
        try {
            const response = await this.apiCall('recommendations/');

            if (response.success) {
                this.updateRecommendationsUI(response.recommendations);
                return response.recommendations;
            }
        } catch (error) {
            console.warn('⚠️ Failed to get recommendations:', error);
            return this.getFallbackRecommendations();
        }
    }

    async getLearningAnalytics() {
        if (!this.isUserAuthenticated()) {
            console.log('📊 Analytics require authentication');
            return null;
        }

        try {
            const response = await this.apiCall('analytics/');

            if (response.success) {
                this.displayAnalytics(response.analytics);
                return response.analytics;
            }
        } catch (error) {
            console.warn('⚠️ Failed to get learning analytics:', error);
            return null;
        }
    }

    // Adaptive difficulty adjustment
    checkForDifficultyAdjustment() {
        const recentQuestions = this.performanceData.questions.slice(-5); // Last 5 questions

        if (recentQuestions.length >= 5) {
            const recentAccuracy = recentQuestions.filter(q => q.isCorrect).length / recentQuestions.length;
            const avgResponseTime = this.calculateAverageResponseTime(recentQuestions);

            const currentDifficulty = this.getCurrentDifficulty();
            let newDifficulty = currentDifficulty;

            // Adjustment logic
            if (recentAccuracy >= 0.8 && avgResponseTime < 6000) {
                // User is doing well - increase difficulty
                newDifficulty = this.getNextDifficulty(currentDifficulty, 'up');
            } else if (recentAccuracy <= 0.4 || avgResponseTime > 12000) {
                // User struggling - decrease difficulty
                newDifficulty = this.getNextDifficulty(currentDifficulty, 'down');
            }

            if (newDifficulty !== currentDifficulty) {
                this.updateDifficulty(newDifficulty);
                this.showNotification(
                    `🎯 Difficulty adjusted to ${newDifficulty} based on your performance`,
                    'info'
                );
            }
        }
    }

    // Caching and offline support
    cacheContent(key, content) {
        try {
            const cacheData = {
                content,
                timestamp: Date.now(),
                expiry: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
            };
            localStorage.setItem(`talontalk_cache_${key}`, JSON.stringify(cacheData));
            this.cache.set(key, cacheData);
        } catch (error) {
            console.warn('⚠️ Failed to cache content:', error);
        }
    }

    getCachedContent(key) {
        try {
            // Check memory cache first
            if (this.cache.has(key)) {
                const cached = this.cache.get(key);
                if (cached.expiry > Date.now()) {
                    return cached.content;
                }
            }

            // Check localStorage
            const cached = localStorage.getItem(`talontalk_cache_${key}`);
            if (cached) {
                const data = JSON.parse(cached);
                if (data.expiry > Date.now()) {
                    this.cache.set(key, data);
                    return data.content;
                }
            }
        } catch (error) {
            console.warn('⚠️ Failed to get cached content:', error);
        }
        return null;
    }

    // UI Updates
    updateProgressUI() {
        const accuracy = this.performanceData.totalQuestions > 0
            ? (this.performanceData.totalCorrect / this.performanceData.totalQuestions * 100).toFixed(1)
            : 0;

        // Update progress indicators
        const progressElements = document.querySelectorAll('[data-progress="accuracy"]');
        progressElements.forEach(el => {
            el.textContent = `${accuracy}%`;
        });

        const streakElements = document.querySelectorAll('[data-progress="streak"]');
        streakElements.forEach(el => {
            el.textContent = this.performanceData.currentStreak;
        });

        // Update session stats
        const sessionStats = document.getElementById('session-stats');
        if (sessionStats) {
            sessionStats.innerHTML = `
                <div class="flex justify-between text-sm text-gray-600">
                    <span>Questions: ${this.performanceData.totalQuestions}</span>
                    <span>Accuracy: ${accuracy}%</span>
                    <span>Streak: ${this.performanceData.currentStreak}</span>
                </div>
            `;
        }
    }

    updateRecommendations(insights) {
        const recommendationsEl = document.getElementById('learning-recommendations');
        if (recommendationsEl && insights.recommendations) {
            const html = insights.recommendations.map(rec =>
                `<div class="bg-blue-50 border-l-4 border-blue-400 p-3 mb-2">
                    <p class="text-blue-800 text-sm">${rec}</p>
                </div>`
            ).join('');

            recommendationsEl.innerHTML = html;
        }
    }

    showLearningInsights(insights) {
        const modal = this.createInsightsModal(insights);
        document.body.appendChild(modal);

        setTimeout(() => {
            modal.remove();
        }, 10000); // Remove after 10 seconds
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${this.getNotificationClass(type)}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    // Utility methods
    async apiCall(endpoint, method = 'GET', data = null) {
        const url = `${this.apiBase}${endpoint}`;
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': this.getCSRFToken()
            }
        };

        if (data) {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(url, options);
        return await response.json();
    }

    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    calculateAverageResponseTime(questions) {
        const responseTimes = questions.map(q => q.responseTime || 5000);
        return responseTimes.length > 0
            ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
            : 0;
    }

    getCurrentDifficulty() {
        return this.adaptiveParams?.difficulty ||
            localStorage.getItem('talontalk_difficulty') ||
            'beginner';
    }

    updateDifficulty(newDifficulty) {
        if (this.adaptiveParams) {
            this.adaptiveParams.difficulty = newDifficulty;
        }
        localStorage.setItem('talontalk_difficulty', newDifficulty);
    }

    getNextDifficulty(current, direction) {
        const levels = ['beginner', 'elementary', 'intermediate', 'upper_intermediate', 'advanced', 'proficient'];
        const currentIndex = levels.indexOf(current);

        if (direction === 'up' && currentIndex < levels.length - 1) {
            return levels[currentIndex + 1];
        } else if (direction === 'down' && currentIndex > 0) {
            return levels[currentIndex - 1];
        }

        return current;
    }

    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }

    isUserAuthenticated() {
        // Check if user is authenticated (adjust based on your auth system)
        return document.body.dataset.authenticated === 'true' ||
            localStorage.getItem('user_authenticated') === 'true';
    }

    saveToLocalStorage() {
        try {
            localStorage.setItem('talontalk_performance', JSON.stringify(this.performanceData));
            localStorage.setItem('talontalk_preloaded_content', JSON.stringify(this.preloadedContent));
        } catch (error) {
            console.warn('⚠️ Failed to save to localStorage:', error);
        }
    }

    loadFromLocalStorage() {
        try {
            const savedPerformance = localStorage.getItem('talontalk_performance');
            if (savedPerformance) {
                const data = JSON.parse(savedPerformance);
                // Only load if it's from the same session or recent
                if (Date.now() - data.startTime < 24 * 60 * 60 * 1000) {
                    this.performanceData = { ...this.performanceData, ...data };
                }
            }

            const savedContent = localStorage.getItem('talontalk_preloaded_content');
            if (savedContent) {
                this.preloadedContent = JSON.parse(savedContent);
            }
        } catch (error) {
            console.warn('⚠️ Failed to load from localStorage:', error);
        }
    }

    getFallbackContent(language, difficulty) {
        return {
            flashcards: [
                {
                    id: 'fallback_1',
                    question: `How do you say "Hello" in ${language}?`,
                    options: ['Hola', 'Adiós', 'Gracias', 'Por favor'],
                    correct_answer: 'Hola',
                    hint: 'Common greeting',
                    explanation: 'Basic greeting in Spanish',
                    difficulty: difficulty,
                    language: language
                }
            ],
            is_fallback: true,
            message: 'Generated from local content library'
        };
    }

    setupPeriodicSync() {
        // Sync every 5 minutes when online
        setInterval(() => {
            if (this.isOnline && this.performanceData.totalQuestions > 0) {
                this.syncPerformanceData();
            }
        }, 5 * 60 * 1000);
    }

    async syncPerformanceData() {
        // Sync any cached performance data
        const cachedData = this.getCachedPerformanceData();
        for (const data of cachedData) {
            try {
                await this.apiCall('track-performance/', 'POST', data);
                this.removeCachedPerformanceData(data.session_id);
            } catch (error) {
                console.warn('⚠️ Failed to sync performance data:', error);
            }
        }
    }

    // Additional helper methods would go here...
    getNotificationClass(type) {
        const classes = {
            success: 'bg-green-100 border border-green-400 text-green-700',
            error: 'bg-red-100 border border-red-400 text-red-700',
            warning: 'bg-yellow-100 border border-yellow-400 text-yellow-700',
            info: 'bg-blue-100 border border-blue-400 text-blue-700'
        };
        return classes[type] || classes.info;
    }
}

// Initialize the adaptive learning system when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.talonTalkAL = new TalonTalkAdaptiveLearning();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TalonTalkAdaptiveLearning;
}

from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
import json

# Create your models here.


class Lesson(models.Model):
    """
    Enhanced Lesson Model with C.A.R.E. Framework Support

    C.A.R.E. = Contextualize, Acquire, Reinforce, Extend
    Each lesson now follows this pedagogical framework
    """

    CARE_PHASES = [
        ("contextualize", "Contextualize - Set the scene"),
        ("acquire", "Acquire - Learn new concepts"),
        ("reinforce", "Reinforce - Practice and solidify"),
        ("extend", "Extend - Apply in new contexts"),
    ]

    DIFFICULTY_LEVELS = [
        (1, "Beginner"),
        (2, "Elementary"),
        (3, "Intermediate"),
        (4, "Upper Intermediate"),
        (5, "Advanced"),
    ]

    # Core lesson fields
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)

    # C.A.R.E. Framework Integration
    care_phase = models.CharField(
        max_length=20,
        choices=CARE_PHASES,
        default="contextualize",
        help_text="Current C.A.R.E. phase focus for this lesson",
    )

    # Enhanced metadata
    difficulty_level = models.PositiveIntegerField(
        choices=DIFFICULTY_LEVELS,
        default=1,
        help_text="Lesson difficulty from 1 (Beginner) to 5 (Advanced)",
    )
    estimated_duration = models.PositiveIntegerField(
        default=15, help_text="Estimated completion time in minutes"
    )
    target_language = models.CharField(
        max_length=20, default="spanish", help_text="Target language being taught"
    )

    # Progressive hints system
    hint_levels = models.JSONField(
        default=list,
        help_text="Progressive hints: ['Level 1 hint', 'Level 2 hint', 'Final hint']",
    )

    # Learning objectives
    learning_objectives = models.JSONField(
        default=list, help_text="List of learning objectives for this lesson"
    )

    # Content tracking
    prerequisite_lessons = models.ManyToManyField(
        "self",
        symmetrical=False,
        blank=True,
        help_text="Lessons that should be completed before this one",
    )

    # Analytics
    completion_rate = models.FloatField(default=0.0)
    average_score = models.FloatField(default=0.0)
    total_attempts = models.PositiveIntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["order", "difficulty_level"]
        indexes = [
            models.Index(fields=["care_phase", "difficulty_level"]),
            models.Index(fields=["target_language", "is_active"]),
            models.Index(fields=["difficulty_level", "completion_rate"]),
        ]

    def __str__(self):
        return f"{self.title} ({self.get_care_phase_display()}) - Level {self.difficulty_level}"

    @property
    def success_rate(self):
        """Calculate lesson success rate"""
        if self.total_attempts == 0:
            return 0.0
        return self.completion_rate * 100

    def get_next_hint(self, current_hint_level=0):
        """Get progressive hint based on user's current level"""
        if current_hint_level < len(self.hint_levels):
            return self.hint_levels[current_hint_level]
        return "You've got this! Keep trying."

    def update_analytics(self, completed=False, score=0):
        """Update lesson analytics"""
        self.total_attempts += 1
        if completed:
            # Update completion rate (weighted average)
            self.completion_rate = (
                self.completion_rate * (self.total_attempts - 1) + 1
            ) / self.total_attempts
            # Update average score
            self.average_score = (
                self.average_score * (self.total_attempts - 1) + score
            ) / self.total_attempts
        self.save()


class Vocabulary(models.Model):
    lesson = models.ForeignKey(
        Lesson, related_name="vocabularies", on_delete=models.CASCADE
    )
    word = models.CharField(max_length=100)
    translation = models.CharField(max_length=100)
    example_sentence = models.TextField(blank=True)

    def __str__(self):
        return f"{self.word} ({self.translation})"


class UserLessonProgress(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE)
    completed = models.BooleanField(default=False)
    xp_earned = models.PositiveIntegerField(default=0)
    last_accessed = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("user", "lesson")

    def __str__(self):
        return f"{self.user.username} - {self.lesson.title}"


# ===================================================================
# NEW CONTENT POOL & ADAPTIVE LEARNING MODELS
# ===================================================================


class ContentItem(models.Model):
    """
    The Atomic Content Pool - Central repository of all reusable learning content.
    Each item represents a single piece of learning material (flashcard, MCQ, etc.)
    """

    CONTENT_TYPES = [
        ("flashcard", "Flashcard"),
        ("mcq", "Multiple Choice Question"),
        ("translation", "Translation Exercise"),
        ("listening", "Listening Exercise"),
        ("grammar", "Grammar Exercise"),
        ("vocabulary", "Vocabulary Exercise"),
        ("conversation", "Conversation Practice"),
    ]

    DIFFICULTY_LEVELS = [
        (1, "Beginner"),
        (2, "Elementary"),
        (3, "Intermediate"),
        (4, "Upper Intermediate"),
        (5, "Advanced"),
    ]

    # Core Content Fields
    type = models.CharField(max_length=20, choices=CONTENT_TYPES, db_index=True)
    question_text = models.TextField(help_text="The main question or prompt")
    answer_text = models.TextField(help_text="The correct answer")

    # Multiple choice options (stored as JSON list)
    choices_json = models.JSONField(
        default=list,
        blank=True,
        help_text="List of choices for MCQ questions: ['Option A', 'Option B', ...]",
    )

    # Additional content fields
    hint_text = models.TextField(blank=True, help_text="Optional hint for the learner")

    # Progressive hints system for C.A.R.E. framework
    progressive_hints = models.JSONField(
        default=list,
        help_text="Progressive hints: ['Gentle nudge', 'More specific hint', 'Almost the answer']",
    )

    explanation_text = models.TextField(
        blank=True, help_text="Explanation shown after answering"
    )
    audio_url = models.URLField(
        blank=True, help_text="Audio file URL for listening exercises"
    )
    image_url = models.URLField(blank=True, help_text="Image URL for visual exercises")

    # Metadata for Content Selection
    difficulty = models.IntegerField(
        choices=DIFFICULTY_LEVELS,
        default=1,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        db_index=True,
    )
    language = models.CharField(
        max_length=10,
        default="spanish",
        db_index=True,
        help_text="Target language being taught",
    )
    tags = models.JSONField(
        default=list,
        help_text="Tags for categorization: ['grammar', 'verbs', 'present-tense']",
    )

    # Content Management
    is_active = models.BooleanField(default=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Performance tracking (aggregated from UserContentPerformance)
    total_attempts = models.PositiveIntegerField(default=0)
    total_correct = models.PositiveIntegerField(default=0)
    average_difficulty_score = models.FloatField(default=0.0)

    class Meta:
        indexes = [
            models.Index(fields=["type", "difficulty", "language"]),
            models.Index(fields=["language", "is_active"]),
            models.Index(fields=["difficulty", "average_difficulty_score"]),
        ]
        ordering = ["difficulty", "created_at"]

    def __str__(self):
        return f"{self.get_type_display()} - {self.question_text[:50]}..."

    @property
    def success_rate(self):
        """Calculate overall success rate for this content item"""
        if self.total_attempts == 0:
            return 0.0
        return (self.total_correct / self.total_attempts) * 100

    @property
    def choices_list(self):
        """Return choices as a Python list"""
        return self.choices_json if self.choices_json else []

    def add_performance_data(self, is_correct):
        """Update aggregated performance stats"""
        self.total_attempts += 1
        if is_correct:
            self.total_correct += 1
        self.save(update_fields=["total_attempts", "total_correct"])

    def get_progressive_hint(self, hint_level=0):
        """Get progressive hint based on user's attempt level"""
        if not self.progressive_hints or hint_level >= len(self.progressive_hints):
            return self.hint_text or "Keep trying! You can do this."
        return self.progressive_hints[hint_level]


class UserContentPerformance(models.Model):
    """
    Detailed Performance Tracker - One record per user per content item.
    This enables powerful analytics and adaptive learning algorithms.
    """

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="content_performances",
    )
    content_item = models.ForeignKey(
        ContentItem, on_delete=models.CASCADE, related_name="user_performances"
    )

    # Core Performance Metrics
    times_seen = models.PositiveIntegerField(default=0)
    times_correct = models.PositiveIntegerField(default=0)
    last_seen = models.DateTimeField(auto_now=True)

    # Proficiency Scoring (0.0 = No mastery, 1.0 = Full mastery)
    proficiency_score = models.FloatField(
        default=0.0,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        db_index=True,
        help_text="User's mastery level for this content (0.0 to 1.0)",
    )

    # Advanced Analytics Fields
    average_response_time = models.FloatField(
        null=True, blank=True, help_text="Average time to answer in seconds"
    )
    consecutive_correct = models.IntegerField(
        default=0, help_text="Current streak of correct answers"
    )
    last_incorrect_date = models.DateTimeField(
        null=True, blank=True, help_text="When user last got this item wrong"
    )

    # Hint Usage Tracking
    times_hint_used = models.PositiveIntegerField(default=0)

    # Spaced Repetition Fields
    next_review_date = models.DateTimeField(
        null=True, blank=True, help_text="When this item should be reviewed again"
    )
    repetition_interval = models.PositiveIntegerField(
        default=1, help_text="Days until next review (spaced repetition)"
    )

    # Timestamps
    first_seen = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ("user", "content_item")
        indexes = [
            models.Index(fields=["user", "proficiency_score"]),
            models.Index(fields=["content_item", "proficiency_score"]),
            models.Index(fields=["user", "last_seen"]),
            models.Index(fields=["user", "next_review_date"]),
            models.Index(fields=["proficiency_score", "last_seen"]),
        ]
        ordering = ["-last_seen"]

    def __str__(self):
        return f"{self.user.username} - {self.content_item.question_text[:30]}... ({self.proficiency_score:.2f})"

    @property
    def accuracy_rate(self):
        """Calculate accuracy percentage"""
        if self.times_seen == 0:
            return 0.0
        return (self.times_correct / self.times_seen) * 100

    @property
    def needs_review(self):
        """Check if this item is due for review based on spaced repetition"""
        if not self.next_review_date:
            return True
        return timezone.now() >= self.next_review_date

    def record_attempt(self, is_correct, response_time=None, used_hint=False):
        """Record a learning attempt and update proficiency"""
        self.times_seen += 1

        if is_correct:
            self.times_correct += 1
            self.consecutive_correct += 1
            # Improve proficiency on correct answers
            self.proficiency_score = min(1.0, self.proficiency_score + 0.1)
            # Increase review interval for spaced repetition
            self.repetition_interval = min(30, self.repetition_interval * 2)
        else:
            self.consecutive_correct = 0
            self.last_incorrect_date = timezone.now()
            # Decrease proficiency on incorrect answers
            self.proficiency_score = max(0.0, self.proficiency_score - 0.05)
            # Reset review interval
            self.repetition_interval = 1

        if used_hint:
            self.times_hint_used += 1

        # Update average response time
        if response_time:
            if self.average_response_time:
                # Weighted average: 70% old, 30% new
                self.average_response_time = (self.average_response_time * 0.7) + (
                    response_time * 0.3
                )
            else:
                self.average_response_time = response_time

        # Set next review date
        self.next_review_date = timezone.now() + timezone.timedelta(
            days=self.repetition_interval
        )

        self.save()

        # Update content item's aggregated stats
        self.content_item.add_performance_data(is_correct)


class UserLearningProfile(models.Model):
    """
    Simplified Overall Learning Profile - User's general progress and preferences.
    Detailed performance is tracked via UserContentPerformance.
    """

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="adaptive_learning_profile",
    )

    # Overall Progress
    total_xp = models.PositiveIntegerField(default=0)
    current_streak = models.IntegerField(default=0)
    longest_streak = models.IntegerField(default=0)

    # Learning Preferences
    preferred_difficulty = models.IntegerField(
        choices=ContentItem.DIFFICULTY_LEVELS, default=1
    )
    preferred_content_types = models.JSONField(default=list)
    daily_goal_minutes = models.PositiveIntegerField(default=15)

    # Session Tracking
    total_sessions = models.PositiveIntegerField(default=0)
    average_accuracy = models.FloatField(default=0.0)
    learning_pace = models.CharField(max_length=20, default="moderate")
    learning_style = models.CharField(max_length=20, default="visual")
    strengths = models.JSONField(default=list)
    weaknesses = models.JSONField(default=list)
    learning_patterns = models.JSONField(default=dict)

    # Activity Tracking
    total_study_time = models.PositiveIntegerField(
        default=0, help_text="Total minutes studied"
    )
    last_active_date = models.DateField(auto_now=True)

    # Adaptive Learning Settings
    adaptive_difficulty = models.BooleanField(default=True)
    spaced_repetition_enabled = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=["last_active_date"]),
            models.Index(fields=["total_xp"]),
        ]

    def __str__(self):
        return f"{self.user.username}'s Learning Profile (XP: {self.total_xp})"

    @property
    def average_proficiency(self):
        """Calculate user's average proficiency across all content"""
        performances = self.user.content_performances.all()
        if not performances:
            return 0.0
        return (
            performances.aggregate(avg_proficiency=models.Avg("proficiency_score"))[
                "avg_proficiency"
            ]
            or 0.0
        )

    def get_weak_areas(self, limit=5):
        """Get content areas where user is struggling"""
        return self.user.content_performances.filter(
            proficiency_score__lt=0.5, times_seen__gte=2
        ).order_by("proficiency_score")[:limit]

    def get_content_due_for_review(self, limit=10):
        """Get content items due for spaced repetition review"""
        return self.user.content_performances.filter(
            next_review_date__lte=timezone.now()
        ).order_by("next_review_date")[:limit]


class UserLessonQueue(models.Model):
    """
    Personalized Lesson Queue - Lightweight model storing user's learning playlist.
    Contains ordered list of ContentItem IDs for instant delivery.
    """

    QUEUE_STATUSES = [
        ("active", "Active"),
        ("paused", "Paused"),
        ("completed", "Completed"),
    ]

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="lesson_queues"
    )

    # Queue Content (List of ContentItem primary keys)
    ordered_content_ids = models.JSONField(
        default=list, help_text="Ordered list of ContentItem IDs: [1, 5, 23, 8, ...]"
    )

    # Queue Metadata
    queue_type = models.CharField(
        max_length=20,
        default="daily",
        choices=[
            ("daily", "Daily Learning"),
            ("review", "Spaced Repetition Review"),
            ("weak_areas", "Struggling Topics"),
            ("new_content", "New Material"),
        ],
    )
    status = models.CharField(max_length=10, choices=QUEUE_STATUSES, default="active")

    # Progress Tracking
    current_position = models.PositiveIntegerField(default=0)
    total_items = models.PositiveIntegerField(default=0)

    # Scheduling
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(help_text="When this queue should be refreshed")

    class Meta:
        indexes = [
            models.Index(fields=["user", "status", "expires_at"]),
            models.Index(fields=["user", "queue_type"]),
        ]
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.user.username}'s {self.get_queue_type_display()} Queue ({self.get_remaining_count()} remaining)"

    @property
    def is_expired(self):
        """Check if queue has expired and needs refresh"""
        return timezone.now() >= self.expires_at

    @property
    def progress_percentage(self):
        """Calculate completion percentage"""
        if self.total_items == 0:
            return 0
        return min(100, (self.current_position / self.total_items) * 100)

    def get_remaining_count(self):
        """Get number of items remaining in queue"""
        return max(0, self.total_items - self.current_position)

    def get_next_content_ids(self, count=5):
        """Get next N content IDs from the queue"""
        start_idx = self.current_position
        end_idx = min(start_idx + count, len(self.ordered_content_ids))
        return self.ordered_content_ids[start_idx:end_idx]

    def advance_position(self, count=1):
        """Move forward in the queue"""
        self.current_position = min(
            self.current_position + count, len(self.ordered_content_ids)
        )
        self.save(update_fields=["current_position"])

    def reset_queue(self, new_content_ids):
        """Replace queue with new content"""
        self.ordered_content_ids = new_content_ids
        self.total_items = len(new_content_ids)
        self.current_position = 0
        self.save(
            update_fields=["ordered_content_ids", "total_items", "current_position"]
        )


# Helper function for creating sample content
def create_sample_content():
    """Create some sample content items for testing"""
    sample_items = [
        {
            "type": "flashcard",
            "question_text": '¿Cómo se dice "hello" en español?',
            "answer_text": "Hola",
            "difficulty": 1,
            "language": "spanish",
            "tags": ["greetings", "basic"],
        },
        {
            "type": "mcq",
            "question_text": 'What does "Gracias" mean?',
            "answer_text": "Thank you",
            "choices_json": ["Thank you", "Please", "Excuse me", "You're welcome"],
            "difficulty": 1,
            "language": "spanish",
            "tags": ["politeness", "basic"],
        },
        {
            "type": "translation",
            "question_text": 'Translate: "I am learning Spanish"',
            "answer_text": "Estoy aprendiendo español",
            "hint_text": 'Use "estoy" for "I am" + gerund',
            "difficulty": 2,
            "language": "spanish",
            "tags": ["present_continuous", "self_description"],
        },
    ]

    created_items = []
    for item_data in sample_items:
        item, created = ContentItem.objects.get_or_create(
            question_text=item_data["question_text"], defaults=item_data
        )
        if created:
            created_items.append(item)

    return created_items

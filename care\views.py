"""
C.A.R.E. Framework Views
Pedagogical framework implementation using dynamic content
"""

from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.conf import settings
import json
import requests
from lessons.models import Lesson, UserLessonProgress


@login_required
def care_lesson_view(request, lesson_id=None):
    """
    Main C.A.R.E. lesson view that renders the interactive lesson template
    """
    # Use default lesson if none specified
    if not lesson_id:
        lesson_id = "restaurant_ordering"

    # Try to get a real lesson from database, fallback to static data
    lesson = None
    try:
        lesson = get_object_or_404(Lesson, id=lesson_id, is_active=True)
    except:
        # Create a mock lesson object for static data
        class MockLesson:
            def __init__(self):
                self.id = lesson_id
                self.title = "Restaurant Ordering in Spanish"
                self.description = "Learn to order food and drinks at a Spanish restaurant with confidence"
                self.difficulty_level = "beginner"
                self.language = "spanish"

        lesson = MockLesson()

    # Get user progress
    progress = None
    if hasattr(lesson, "id") and isinstance(lesson.id, int):
        progress, _ = UserLessonProgress.objects.get_or_create(
            user=request.user,
            lesson=lesson,
            defaults={"completed": False, "xp_earned": 0},
        )

    context = {
        "lesson": lesson,
        "progress": progress,
        "lesson_id": lesson_id,
        "user": request.user,
    }

    return render(request, "care/lesson.html", context)


@require_http_methods(["GET"])
def care_phase_data(request, phase):
    """
    API endpoint to get dynamic data for each C.A.R.E. phase
    """
    lesson_id = request.GET.get("lesson", "restaurant_ordering")

    try:
        # Import the existing care test views functions
        from care_test_views import (
            care_contextualize,
            care_acquire,
            care_reinforce,
            care_extend,
        )

        # Route to appropriate phase handler
        phase_handlers = {
            "contextualize": care_contextualize,
            "acquire": care_acquire,
            "reinforce": care_reinforce,
            "extend": care_extend,
        }

        if phase not in phase_handlers:
            return JsonResponse(
                {"success": False, "error": "Invalid phase"}, status=400
            )

        # Call the appropriate handler and get the response
        handler_response = phase_handlers[phase](request)

        # If it's already a JsonResponse, extract the data
        if hasattr(handler_response, "content"):
            import json

            content = json.loads(handler_response.content.decode("utf-8"))
        else:
            content = handler_response

        # Return in the format expected by the frontend
        return JsonResponse({"success": True, "content": content})

    except Exception as e:
        return JsonResponse({"success": False, "error": str(e)}, status=500)


@require_http_methods(["POST"])
@csrf_exempt
def care_submit_answer(request, phase):
    """
    Handle answer submissions for each C.A.R.E. phase
    """
    try:
        data = json.loads(request.body)
        answer = data.get("answer")
        question_id = data.get("question_id")
        lesson_id = data.get("lesson", "restaurant_ordering")

        # Basic validation
        if not answer or not question_id:
            return JsonResponse({"error": "Missing answer or question_id"}, status=400)

        # For now, use simple correct/incorrect logic
        # In a real implementation, this would validate against the correct answer
        is_correct = True  # Placeholder - replace with real validation

        # Generate feedback based on phase
        feedback = generate_phase_feedback(phase, is_correct, answer)

        return JsonResponse(
            {
                "success": True,
                "correct": is_correct,
                "feedback": feedback,
                "next_phase": get_next_phase(phase) if is_correct else None,
            }
        )

    except json.JSONDecodeError:
        return JsonResponse({"error": "Invalid JSON"}, status=400)
    except Exception as e:
        return JsonResponse({"error": str(e)}, status=500)


def generate_phase_feedback(phase, is_correct, answer):
    """Generate contextual feedback based on the C.A.R.E. phase"""

    feedback_templates = {
        "contextualize": {
            "correct": "Excellent! You've understood the context. Now let's learn some key concepts.",
            "incorrect": "Not quite. Let's review the scenario again and focus on the key details.",
        },
        "acquire": {
            "correct": "Great job learning this new concept! Let's practice it now.",
            "incorrect": "Don't worry, this is new material. Let's try a different approach.",
        },
        "reinforce": {
            "correct": "Perfect! You're really getting the hang of this.",
            "incorrect": "Good attempt! Practice makes perfect - let's try again.",
        },
        "extend": {
            "correct": "Outstanding! You've applied your knowledge creatively.",
            "incorrect": "Interesting attempt! Let's think about how to apply what we learned.",
        },
    }

    feedback_type = "correct" if is_correct else "incorrect"
    return feedback_templates.get(phase, {}).get(feedback_type, "Good effort!")


def get_next_phase(current_phase):
    """Get the next phase in the C.A.R.E. sequence"""
    phase_sequence = ["contextualize", "acquire", "reinforce", "extend"]

    try:
        current_index = phase_sequence.index(current_phase)
        if current_index < len(phase_sequence) - 1:
            return phase_sequence[current_index + 1]
    except ValueError:
        pass

    return None  # No next phase or invalid current phase


@require_http_methods(["GET"])
def care_ai_tutor_chat(request):
    """
    AI Tutor chat interface for contextual help
    """
    user_message = request.GET.get("message", "")
    lesson_id = request.GET.get("lesson", "restaurant_ordering")
    phase = request.GET.get("phase", "contextualize")

    if not user_message:
        return JsonResponse({"error": "No message provided"}, status=400)

    # Generate AI tutor response (placeholder - integrate with actual LLM)
    tutor_response = generate_tutor_response(user_message, lesson_id, phase)

    return JsonResponse(
        {
            "success": True,
            "response": tutor_response,
            "phase": phase,
            "lesson": lesson_id,
        }
    )


def generate_tutor_response(user_message, lesson_id, phase):
    """
    Generate AI tutor response based on context
    This is a placeholder - integrate with actual LLM service
    """

    phase_contexts = {
        "contextualize": "I'm here to help you understand the scenario and context.",
        "acquire": "Let me help you learn these new concepts step by step.",
        "reinforce": "Great question! Let's practice this together.",
        "extend": "I love seeing you apply your knowledge creatively!",
    }

    context = phase_contexts.get(phase, "I'm here to help!")

    # Simple response generation (replace with actual LLM integration)
    if "how" in user_message.lower():
        return f"{context} Here's how you can approach this..."
    elif "what" in user_message.lower():
        return f"{context} What you're looking for is..."
    elif "why" in user_message.lower():
        return f"{context} The reason is..."
    else:
        return f"{context} That's a great question! Let me help you with that."


@require_http_methods(["GET"])
def care_lesson_list(request):
    """
    Get list of available C.A.R.E. lessons
    """
    # For now, return static lesson data
    # In production, this would query the database

    lessons = [
        {
            "id": "restaurant_ordering",
            "title": "Restaurant Ordering in Spanish",
            "description": "Learn to order food and drinks confidently",
            "difficulty": "beginner",
            "estimated_time": "15 minutes",
            "completed": False,
        },
        {
            "id": "hotel_checkin",
            "title": "Hotel Check-in Conversations",
            "description": "Master hotel vocabulary and phrases",
            "difficulty": "beginner",
            "estimated_time": "20 minutes",
            "completed": False,
        },
        {
            "id": "shopping_market",
            "title": "Shopping at Local Markets",
            "description": "Navigate markets and bargain effectively",
            "difficulty": "intermediate",
            "estimated_time": "25 minutes",
            "completed": False,
        },
    ]

    return JsonResponse({"success": True, "lessons": lessons})

"""
LLM Service for TalonTalk Language Learning
Handles flashcard generation, grading, and explanations using multiple LLM providers
Supports OpenAI, OpenRouter, DeepSeek, and other providers for optimal language learning
"""

import openai
import json
import logging
import requests
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum
import random
import os

from .llm_config import LLMConfig, LLMProvider, get_recommended_config

logger = logging.getLogger(__name__)


class DifficultyLevel(Enum):
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"


class ExerciseType(Enum):
    TRANSLATION = "translation"
    FILL_BLANK = "fill_blank"
    MULTIPLE_CHOICE = "multiple_choice"
    SENTENCE_CONSTRUCTION = "sentence_construction"
    LISTENING_COMPREHENSION = "listening_comprehension"


@dataclass
class FlashcardRequest:
    language: str
    target_language: str
    difficulty: DifficultyLevel
    exercise_type: ExerciseType
    vocabulary: Optional[List[str]] = None
    grammar_topic: Optional[str] = None
    context: Optional[str] = None


@dataclass
class FlashcardResponse:
    question: str
    correct_answer: str
    options: Optional[List[str]] = None
    explanation: str = ""
    hint: str = ""
    example_sentence: str = ""
    pronunciation_guide: str = ""


class LLMFlashcardService:
    def __init__(self, config: Optional[LLMConfig] = None):
        """
        Initialize LLM service with automatic provider selection

        If no config provided, automatically selects the best available provider:
        1. OpenRouter (free Mistral) - Best value, excellent multilingual
        2. DeepSeek - Excellent reasoning, very cost-effective
        3. OpenAI GPT-4o Mini - Reliable, good quality
        4. Fallback to demo mode if no API keys available
        """
        self.config = config or get_recommended_config()
        self.client = self._initialize_client()

    def _initialize_client(self):
        """Initialize the appropriate client based on provider"""
        if not self.config.api_key and self.config.provider != LLMProvider.OLLAMA:
            logger.warning(
                f"No API key found for {self.config.provider.value}, running in demo mode"
            )
            return None

        try:
            if self.config.provider in [
                LLMProvider.OPENAI,
                LLMProvider.OPENROUTER,
                LLMProvider.DEEPSEEK,
                LLMProvider.OLLAMA,  # Added Ollama support
            ]:
                return openai.OpenAI(
                    base_url=self.config.base_url,
                    api_key=self.config.api_key or "dummy",
                )
            else:
                # For providers like Anthropic that need custom handling
                return None
        except Exception as e:
            logger.error(
                f"Failed to initialize {self.config.provider.value} client: {e}"
            )
            return None

    def _make_llm_request(
        self, messages: List[Dict], temperature: float = 0.7, max_tokens: int = 500
    ) -> str:
        """Make LLM request handling different providers"""
        try:
            if self.config.provider == LLMProvider.ANTHROPIC and self.config.api_key:
                # Handle Anthropic API directly
                response = requests.post(
                    "https://api.anthropic.com/v1/messages",
                    headers=self.config.get_headers(),
                    json={
                        "model": self.config.model_name,
                        "max_tokens": max_tokens,
                        "messages": messages,
                    },
                )
                return response.json()["content"][0]["text"]

            elif self.client:
                # Handle OpenAI-compatible APIs (OpenAI, OpenRouter, DeepSeek, Ollama)
                system_message = {
                    "role": "system",
                    "content": "You are an expert language teacher. You MUST respond with ONLY valid JSON. No explanations, no thinking, no markdown. Just pure JSON.",
                }

                if self.config.provider == LLMProvider.OLLAMA:
                    # Special handling for Ollama - be more explicit about JSON-only
                    system_message["content"] = (
                        "Respond with valid JSON only. No text before or after the JSON object."
                    )

                response = self.client.chat.completions.create(
                    model=self.config.model_name,
                    messages=[
                        system_message,
                        *messages,
                    ],
                    temperature=temperature,
                    max_tokens=max_tokens,
                )
                return response.choices[0].message.content
            else:
                return None

        except Exception as e:
            logger.error(
                f"Error making LLM request with {self.config.provider.value}: {e}"
            )
            return None

    def generate_flashcard(self, request: FlashcardRequest) -> FlashcardResponse:
        """Generate a flashcard based on the request parameters"""
        try:
            prompt = self._build_flashcard_prompt(request)

            content = self._make_llm_request([{"role": "user", "content": prompt}])

            if content:
                return self._parse_flashcard_response(content)
            else:
                # Fallback for demo/testing without API key
                return self._generate_demo_flashcard(request)

        except Exception as e:
            logger.error(f"Error generating flashcard: {e}")
            return self._generate_fallback_flashcard(request)

    def generate_lesson(
        self, request: FlashcardRequest, lesson_length: int = 5
    ) -> List[FlashcardResponse]:
        """Generate a lesson consisting of multiple related flashcards"""
        try:
            prompt = self._build_lesson_prompt(request, lesson_length)

            content = self._make_llm_request(
                [{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=2000,  # Increased for multiple flashcards
            )

            if content:
                return self._parse_lesson_response(content, lesson_length)
            else:
                # Fallback for demo/testing without API key
                return self._generate_demo_lesson(request, lesson_length)

        except Exception as e:
            logger.error(f"Error generating lesson: {e}")
            return self._generate_fallback_lesson(request, lesson_length)

    def grade_answer(
        self, question: str, correct_answer: str, user_answer: str, language: str
    ) -> Dict:
        """Grade user's answer and provide feedback"""
        try:
            prompt = f"""
            Grade this language learning answer:
            
            Question: {question}
            Correct Answer: {correct_answer}
            User Answer: {user_answer}
            Language: {language}
            
            Provide response in JSON format:
            {{
                "score": 0-100,
                "is_correct": true/false,
                "feedback": "detailed feedback",
                "suggestions": ["improvement suggestions"]
            }}
            """

            content = self._make_llm_request([{"role": "user", "content": prompt}])

            if content:
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    # Fallback if JSON parsing fails
                    return {
                        "score": 50,
                        "is_correct": False,
                        "feedback": content,
                        "suggestions": ["Please try again"],
                    }
            else:
                # Fallback scoring without API
                is_correct = (
                    user_answer.lower().strip() == correct_answer.lower().strip()
                )
                return {
                    "score": 100 if is_correct else 0,
                    "is_correct": is_correct,
                    "feedback": (
                        "Correct!"
                        if is_correct
                        else f"The correct answer is: {correct_answer}"
                    ),
                    "suggestions": (
                        [] if is_correct else ["Try to match the exact answer"]
                    ),
                }

        except Exception as e:
            logger.error(f"Error grading answer: {e}")
            return {
                "score": 0,
                "is_correct": False,
                "feedback": "Unable to grade answer at this time",
                "suggestions": ["Please try again later"],
            }

    def _build_flashcard_prompt(self, request: FlashcardRequest) -> str:
        """Build the prompt for flashcard generation"""
        base_prompt = f"""
        Create a {request.exercise_type.value} exercise for learning {request.target_language} 
        at {request.difficulty.value} level.
        """

        if request.vocabulary:
            base_prompt += (
                f"\nFocus on these vocabulary words: {', '.join(request.vocabulary)}"
            )

        if request.grammar_topic:
            base_prompt += f"\nGrammar topic: {request.grammar_topic}"

        if request.context:
            base_prompt += f"\nContext: {request.context}"

        base_prompt += """
        
        IMPORTANT: Respond with ONLY valid JSON, no other text.
        
        Format:
        {
            "question": "the exercise question",
            "correct_answer": "the correct answer",
            "options": ["option1", "option2", "option3", "option4"],
            "explanation": "why this is correct",
            "hint": "helpful hint for the user",
            "example_sentence": "example using the vocabulary/grammar",
            "pronunciation_guide": "phonetic pronunciation if applicable"
        }
        
        For non-multiple choice, use empty array for options: "options": []
        """

        return base_prompt

    def _build_lesson_prompt(
        self, request: FlashcardRequest, lesson_length: int
    ) -> str:
        """Build the prompt for lesson generation"""
        base_prompt = f"""
        Create a lesson of {lesson_length} {request.exercise_type.value} exercises for learning {request.target_language} 
        at {request.difficulty.value} level.
        
        The lesson should:
        - Progress from easier to slightly harder concepts
        - Cover diverse vocabulary and grammar points
        - Each flashcard should be unique and distinct
        - Include varied question types within the exercise type
        """

        if request.vocabulary:
            base_prompt += (
                f"\nIncorporate these vocabulary words: {', '.join(request.vocabulary)}"
            )

        if request.grammar_topic:
            base_prompt += f"\nFocus on grammar topic: {request.grammar_topic}"
        else:
            # Get appropriate vocabulary and grammar for the level
            vocab_words = get_vocabulary_for_level(
                request.target_language, request.difficulty
            )
            grammar_topics = get_grammar_topics(
                request.target_language, request.difficulty
            )
            base_prompt += f"\nUse vocabulary from: {', '.join(vocab_words[:10])}"
            base_prompt += (
                f"\nIncorporate grammar concepts: {', '.join(grammar_topics[:3])}"
            )

        if request.context:
            base_prompt += f"\nAdditional context: {request.context}"

        base_prompt += f"""
        
        IMPORTANT: Respond with ONLY a valid JSON array, no other text.
        
        Format:
        [
            {{
                "question": "the exercise question",
                "correct_answer": "the correct answer",
                "options": ["option1", "option2", "option3", "option4"],
                "explanation": "why this is correct and what it teaches",
                "hint": "helpful hint for the user", 
                "example_sentence": "example sentence using the vocabulary/grammar",
                "pronunciation_guide": "phonetic pronunciation if applicable"
            }},
            {{
                "question": "second exercise question",
                "correct_answer": "second correct answer",
                "options": ["opt1", "opt2", "opt3", "opt4"],
                "explanation": "explanation for second question",
                "hint": "hint for second question",
                "example_sentence": "example for second question",
                "pronunciation_guide": "pronunciation for second question"
            }}
        ]
        
        Generate exactly {lesson_length} flashcards. For non-multiple choice, use empty array for options: "options": []
        Make sure each flashcard is unique and teaches something different.
        """
        return base_prompt

    def _parse_flashcard_response(self, content: str) -> FlashcardResponse:
        """Parse LLM response into FlashcardResponse object"""
        try:
            # Clean up the content - remove any markdown formatting or extra text
            content = content.strip()
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            content = content.strip()

            # Try to find JSON object if there's extra text
            start_idx = content.find("{")
            end_idx = content.rfind("}") + 1
            if start_idx != -1 and end_idx > start_idx:
                content = content[start_idx:end_idx]

            data = json.loads(content)

            # Create response object
            response = FlashcardResponse(
                question=data.get("question", ""),
                correct_answer=data.get("correct_answer", ""),
                options=data.get("options"),
                explanation=data.get("explanation", ""),
                hint=data.get("hint", ""),
                example_sentence=data.get("example_sentence", ""),
                pronunciation_guide=data.get("pronunciation_guide", ""),
            )

            # Validate multiple choice options
            response = self._validate_multiple_choice(response)

            return response
        except Exception as e:
            logger.warning(f"Failed to parse flashcard JSON: {e}")
            # Fallback parsing if JSON is malformed
            lines = content.split("\n")
            return FlashcardResponse(
                question=lines[0] if lines else "Sample question",
                correct_answer=lines[1] if len(lines) > 1 else "Sample answer",
                explanation="Generated explanation",
                hint="Generated hint",
            )

    def _parse_lesson_response(
        self, content: str, lesson_length: int
    ) -> List[FlashcardResponse]:
        """Parse LLM response into list of FlashcardResponse objects"""
        try:
            # Clean up the content - remove any markdown formatting or extra text
            content = content.strip()
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            content = content.strip()

            # Try to find JSON array if there's extra text
            start_idx = content.find("[")
            end_idx = content.rfind("]") + 1
            if start_idx != -1 and end_idx > start_idx:
                content = content[start_idx:end_idx]

            data = json.loads(content)
            if isinstance(data, list):
                flashcards = []
                for item in data:
                    response = FlashcardResponse(
                        question=item.get("question", ""),
                        correct_answer=item.get("correct_answer", ""),
                        options=item.get("options"),
                        explanation=item.get("explanation", ""),
                        hint=item.get("hint", ""),
                        example_sentence=item.get("example_sentence", ""),
                        pronunciation_guide=item.get("pronunciation_guide", ""),
                    )
                    # Validate multiple choice options
                    response = self._validate_multiple_choice(response)
                    flashcards.append(response)
                return flashcards[
                    :lesson_length
                ]  # Ensure we don't exceed requested length
        except Exception as e:
            logger.warning(
                f"Failed to parse lesson response as JSON: {e}, trying fallback parsing"
            )

        # Fallback: try to extract individual flashcards from text
        return self._parse_lesson_fallback(content, lesson_length)

    def _parse_lesson_fallback(
        self, content: str, lesson_length: int
    ) -> List[FlashcardResponse]:
        """Fallback parsing when JSON parsing fails"""
        # Simple text-based parsing as last resort
        flashcards = []
        lines = content.split("\n")
        current_card = {}

        for line in lines:
            line = line.strip()
            if line.startswith("Question:") or line.startswith("question:"):
                if current_card.get("question"):
                    # Save previous card and start new one
                    flashcards.append(self._create_flashcard_from_dict(current_card))
                    current_card = {}
                current_card["question"] = line.split(":", 1)[1].strip()
            elif line.startswith("Answer:") or line.startswith("correct_answer:"):
                current_card["correct_answer"] = line.split(":", 1)[1].strip()
            elif line.startswith("Explanation:") or line.startswith("explanation:"):
                current_card["explanation"] = line.split(":", 1)[1].strip()

        # Add the last card
        if current_card.get("question"):
            flashcards.append(self._create_flashcard_from_dict(current_card))

        # If we still don't have enough cards, generate fallback ones
        while len(flashcards) < lesson_length:
            flashcards.append(
                FlashcardResponse(
                    question=f"Practice question {len(flashcards) + 1}",
                    correct_answer="Sample answer",
                    explanation="This is a fallback exercise",
                    hint="Try your best!",
                )
            )

        return flashcards[:lesson_length]

    def _create_flashcard_from_dict(self, card_dict: dict) -> FlashcardResponse:
        """Helper to create FlashcardResponse from dictionary"""
        return FlashcardResponse(
            question=card_dict.get("question", "Sample question"),
            correct_answer=card_dict.get("correct_answer", "Sample answer"),
            options=card_dict.get("options"),
            explanation=card_dict.get("explanation", "Sample explanation"),
            hint=card_dict.get("hint", "Sample hint"),
            example_sentence=card_dict.get("example_sentence", ""),
            pronunciation_guide=card_dict.get("pronunciation_guide", ""),
        )

    def _validate_multiple_choice(
        self, response: FlashcardResponse
    ) -> FlashcardResponse:
        """
        Validate that multiple choice questions have the correct answer in options.
        If not, fix it by adding or replacing options.
        """
        if not response.options or len(response.options) == 0:
            # Not a multiple choice question
            return response

        # Check if correct answer is in options (case-insensitive)
        correct_answer = response.correct_answer.strip().lower()
        options_lower = [opt.strip().lower() for opt in response.options]

        if correct_answer not in options_lower:
            logger.warning(
                f"Correct answer '{response.correct_answer}' not found in options {response.options}"
            )

            # Simple fix: replace a random option with the correct answer
            import random

            fixed_options = response.options.copy()
            random_index = random.randint(0, len(fixed_options) - 1)
            fixed_options[random_index] = response.correct_answer

            logger.info(f"Fixed options: {response.options} -> {fixed_options}")

            # Return new response with fixed options
            return FlashcardResponse(
                question=response.question,
                correct_answer=response.correct_answer,
                options=fixed_options,
                explanation=response.explanation,
                hint=response.hint,
                example_sentence=response.example_sentence,
                pronunciation_guide=response.pronunciation_guide,
            )

        return response

    def generate_flashcard(self, request: FlashcardRequest) -> FlashcardResponse:
        """Generate a flashcard based on the request parameters"""
        try:
            prompt = self._build_flashcard_prompt(request)

            content = self._make_llm_request([{"role": "user", "content": prompt}])

            if content:
                return self._parse_flashcard_response(content)
            else:
                # Fallback for demo/testing without API key
                return self._generate_demo_flashcard(request)

        except Exception as e:
            logger.error(f"Error generating flashcard: {e}")
            return self._generate_fallback_flashcard(request)

    def generate_lesson(
        self, request: FlashcardRequest, lesson_length: int = 5
    ) -> List[FlashcardResponse]:
        """Generate a lesson consisting of multiple related flashcards"""
        try:
            prompt = self._build_lesson_prompt(request, lesson_length)

            content = self._make_llm_request(
                [{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=2000,  # Increased for multiple flashcards
            )

            if content:
                return self._parse_lesson_response(content, lesson_length)
            else:
                # Fallback for demo/testing without API key
                return self._generate_demo_lesson(request, lesson_length)

        except Exception as e:
            logger.error(f"Error generating lesson: {e}")
            return self._generate_fallback_lesson(request, lesson_length)

    def grade_answer(
        self, question: str, correct_answer: str, user_answer: str, language: str
    ) -> Dict:
        """Grade user's answer and provide feedback"""
        try:
            prompt = f"""
            Grade this language learning answer:
            
            Question: {question}
            Correct Answer: {correct_answer}
            User Answer: {user_answer}
            Language: {language}
            
            Provide response in JSON format:
            {{
                "score": 0-100,
                "is_correct": true/false,
                "feedback": "detailed feedback",
                "suggestions": ["improvement suggestions"]
            }}
            """

            content = self._make_llm_request([{"role": "user", "content": prompt}])

            if content:
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    # Fallback if JSON parsing fails
                    return {
                        "score": 50,
                        "is_correct": False,
                        "feedback": content,
                        "suggestions": ["Please try again"],
                    }
            else:
                # Fallback scoring without API
                is_correct = (
                    user_answer.lower().strip() == correct_answer.lower().strip()
                )
                return {
                    "score": 100 if is_correct else 0,
                    "is_correct": is_correct,
                    "feedback": (
                        "Correct!"
                        if is_correct
                        else f"The correct answer is: {correct_answer}"
                    ),
                    "suggestions": (
                        [] if is_correct else ["Try to match the exact answer"]
                    ),
                }

        except Exception as e:
            logger.error(f"Error grading answer: {e}")
            return {
                "score": 0,
                "is_correct": False,
                "feedback": "Unable to grade answer at this time",
                "suggestions": ["Please try again later"],
            }

    def _build_flashcard_prompt(self, request: FlashcardRequest) -> str:
        """Build the prompt for flashcard generation"""
        base_prompt = f"""
        Create a {request.exercise_type.value} exercise for learning {request.target_language} 
        at {request.difficulty.value} level.
        """

        if request.vocabulary:
            base_prompt += (
                f"\nFocus on these vocabulary words: {', '.join(request.vocabulary)}"
            )

        if request.grammar_topic:
            base_prompt += f"\nGrammar topic: {request.grammar_topic}"

        if request.context:
            base_prompt += f"\nContext: {request.context}"

        base_prompt += """
        
        IMPORTANT: Respond with ONLY valid JSON, no other text.
        
        Format:
        {
            "question": "the exercise question",
            "correct_answer": "the correct answer",
            "options": ["option1", "option2", "option3", "option4"],
            "explanation": "why this is correct",
            "hint": "helpful hint for the user",
            "example_sentence": "example using the vocabulary/grammar",
            "pronunciation_guide": "phonetic pronunciation if applicable"
        }
        
        For non-multiple choice, use empty array for options: "options": []
        """

        return base_prompt

    def _build_lesson_prompt(
        self, request: FlashcardRequest, lesson_length: int
    ) -> str:
        """Build the prompt for lesson generation"""
        base_prompt = f"""
        Create a lesson of {lesson_length} {request.exercise_type.value} exercises for learning {request.target_language} 
        at {request.difficulty.value} level.
        
        The lesson should:
        - Progress from easier to slightly harder concepts
        - Cover diverse vocabulary and grammar points
        - Each flashcard should be unique and distinct
        - Include varied question types within the exercise type
        """

        if request.vocabulary:
            base_prompt += (
                f"\nIncorporate these vocabulary words: {', '.join(request.vocabulary)}"
            )

        if request.grammar_topic:
            base_prompt += f"\nFocus on grammar topic: {request.grammar_topic}"
        else:
            # Get appropriate vocabulary and grammar for the level
            vocab_words = get_vocabulary_for_level(
                request.target_language, request.difficulty
            )
            grammar_topics = get_grammar_topics(
                request.target_language, request.difficulty
            )
            base_prompt += f"\nUse vocabulary from: {', '.join(vocab_words[:10])}"
            base_prompt += (
                f"\nIncorporate grammar concepts: {', '.join(grammar_topics[:3])}"
            )

        if request.context:
            base_prompt += f"\nAdditional context: {request.context}"

        base_prompt += f"""
        
        IMPORTANT: Respond with ONLY a valid JSON array, no other text.
        
        Format:
        [
            {{
                "question": "the exercise question",
                "correct_answer": "the correct answer",
                "options": ["option1", "option2", "option3", "option4"],
                "explanation": "why this is correct and what it teaches",
                "hint": "helpful hint for the user", 
                "example_sentence": "example sentence using the vocabulary/grammar",
                "pronunciation_guide": "phonetic pronunciation if applicable"
            }},
            {{
                "question": "second exercise question",
                "correct_answer": "second correct answer",
                "options": ["opt1", "opt2", "opt3", "opt4"],
                "explanation": "explanation for second question",
                "hint": "hint for second question",
                "example_sentence": "example for second question",
                "pronunciation_guide": "pronunciation for second question"
            }}
        ]
        
        Generate exactly {lesson_length} flashcards. For non-multiple choice, use empty array for options: "options": []
        Make sure each flashcard is unique and teaches something different.
        """
        return base_prompt

    def _parse_flashcard_response(self, content: str) -> FlashcardResponse:
        """Parse LLM response into FlashcardResponse object"""
        try:
            # Clean up the content - remove any markdown formatting or extra text
            content = content.strip()
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            content = content.strip()

            # Try to find JSON object if there's extra text
            start_idx = content.find("{")
            end_idx = content.rfind("}") + 1
            if start_idx != -1 and end_idx > start_idx:
                content = content[start_idx:end_idx]

            data = json.loads(content)

            # Create response object
            response = FlashcardResponse(
                question=data.get("question", ""),
                correct_answer=data.get("correct_answer", ""),
                options=data.get("options"),
                explanation=data.get("explanation", ""),
                hint=data.get("hint", ""),
                example_sentence=data.get("example_sentence", ""),
                pronunciation_guide=data.get("pronunciation_guide", ""),
            )

            # Validate multiple choice options
            response = self._validate_multiple_choice(response)

            return response
        except Exception as e:
            logger.warning(f"Failed to parse flashcard JSON: {e}")
            # Fallback parsing if JSON is malformed
            lines = content.split("\n")
            return FlashcardResponse(
                question=lines[0] if lines else "Sample question",
                correct_answer=lines[1] if len(lines) > 1 else "Sample answer",
                explanation="Generated explanation",
                hint="Generated hint",
            )

    def _parse_lesson_response(
        self, content: str, lesson_length: int
    ) -> List[FlashcardResponse]:
        """Parse LLM response into list of FlashcardResponse objects"""
        try:
            # Clean up the content - remove any markdown formatting or extra text
            content = content.strip()
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            content = content.strip()

            # Try to find JSON array if there's extra text
            start_idx = content.find("[")
            end_idx = content.rfind("]") + 1
            if start_idx != -1 and end_idx > start_idx:
                content = content[start_idx:end_idx]

            data = json.loads(content)
            if isinstance(data, list):
                flashcards = []
                for item in data:
                    response = FlashcardResponse(
                        question=item.get("question", ""),
                        correct_answer=item.get("correct_answer", ""),
                        options=item.get("options"),
                        explanation=item.get("explanation", ""),
                        hint=item.get("hint", ""),
                        example_sentence=item.get("example_sentence", ""),
                        pronunciation_guide=item.get("pronunciation_guide", ""),
                    )
                    # Validate multiple choice options
                    response = self._validate_multiple_choice(response)
                    flashcards.append(response)
                return flashcards[
                    :lesson_length
                ]  # Ensure we don't exceed requested length
        except Exception as e:
            logger.warning(
                f"Failed to parse lesson response as JSON: {e}, trying fallback parsing"
            )

        # Fallback: try to extract individual flashcards from text
        return self._parse_lesson_fallback(content, lesson_length)

    def _parse_lesson_fallback(
        self, content: str, lesson_length: int
    ) -> List[FlashcardResponse]:
        """Fallback parsing when JSON parsing fails"""
        # Simple text-based parsing as last resort
        flashcards = []
        lines = content.split("\n")
        current_card = {}

        for line in lines:
            line = line.strip()
            if line.startswith("Question:") or line.startswith("question:"):
                if current_card.get("question"):
                    # Save previous card and start new one
                    flashcards.append(self._create_flashcard_from_dict(current_card))
                    current_card = {}
                current_card["question"] = line.split(":", 1)[1].strip()
            elif line.startswith("Answer:") or line.startswith("correct_answer:"):
                current_card["correct_answer"] = line.split(":", 1)[1].strip()
            elif line.startswith("Explanation:") or line.startswith("explanation:"):
                current_card["explanation"] = line.split(":", 1)[1].strip()

        # Add the last card
        if current_card.get("question"):
            flashcards.append(self._create_flashcard_from_dict(current_card))

        # If we still don't have enough cards, generate fallback ones
        while len(flashcards) < lesson_length:
            flashcards.append(
                FlashcardResponse(
                    question=f"Practice question {len(flashcards) + 1}",
                    correct_answer="Sample answer",
                    explanation="This is a fallback exercise",
                    hint="Try your best!",
                )
            )

        return flashcards[:lesson_length]

    def _create_flashcard_from_dict(self, card_dict: dict) -> FlashcardResponse:
        """Helper to create FlashcardResponse from dictionary"""
        return FlashcardResponse(
            question=card_dict.get("question", "Sample question"),
            correct_answer=card_dict.get("correct_answer", "Sample answer"),
            options=card_dict.get("options"),
            explanation=card_dict.get("explanation", "Sample explanation"),
            hint=card_dict.get("hint", "Sample hint"),
            example_sentence=card_dict.get("example_sentence", ""),
            pronunciation_guide=card_dict.get("pronunciation_guide", ""),
        )

    def _generate_demo_flashcard(self, request: FlashcardRequest) -> FlashcardResponse:
        """Generate demo flashcards for testing without API"""
        demo_cards = {
            "spanish": {
                ExerciseType.TRANSLATION: FlashcardResponse(
                    question="Translate: 'Hello, how are you?'",
                    correct_answer="Hola, ¿cómo estás?",
                    explanation="This is a basic greeting in Spanish",
                    hint="Remember the upside-down question mark at the beginning",
                    example_sentence="Hola, ¿cómo estás? Yo estoy bien.",
                    pronunciation_guide="OH-lah, KOH-moh ehs-TAHS",
                ),
                ExerciseType.MULTIPLE_CHOICE: FlashcardResponse(
                    question="What does 'gato' mean in English?",
                    correct_answer="cat",
                    options=["dog", "cat", "bird", "fish"],
                    explanation="'Gato' is the Spanish word for cat",
                    hint="Think of a common pet that meows",
                    example_sentence="Mi gato es muy bonito.",
                    pronunciation_guide="GAH-toh",
                ),
            }
        }

        lang_cards = demo_cards.get(
            request.target_language.lower(), demo_cards["spanish"]
        )
        return lang_cards.get(request.exercise_type, list(lang_cards.values())[0])

    def _generate_demo_lesson(
        self, request: FlashcardRequest, lesson_length: int
    ) -> List[FlashcardResponse]:
        """Generate demo lesson for testing without API"""
        demo_lessons = {
            "spanish": {
                ExerciseType.MULTIPLE_CHOICE: [
                    FlashcardResponse(
                        question="What does 'hola' mean?",
                        correct_answer="hello",
                        options=["goodbye", "hello", "please", "thank you"],
                        explanation="'Hola' is the most common greeting in Spanish",
                        hint="It's used when meeting someone",
                        example_sentence="Hola, ¿cómo estás?",
                        pronunciation_guide="OH-lah",
                    ),
                    FlashcardResponse(
                        question="Which word means 'cat' in Spanish?",
                        correct_answer="gato",
                        options=["perro", "gato", "pájaro", "pez"],
                        explanation="'Gato' is the Spanish word for a male cat",
                        hint="It's a common pet that meows",
                        example_sentence="Mi gato es muy bonito.",
                        pronunciation_guide="GAH-toh",
                    ),
                    FlashcardResponse(
                        question="How do you say 'thank you' in Spanish?",
                        correct_answer="gracias",
                        options=["hola", "adiós", "gracias", "por favor"],
                        explanation="'Gracias' expresses gratitude in Spanish",
                        hint="You say this when someone helps you",
                        example_sentence="Muchas gracias por tu ayuda.",
                        pronunciation_guide="GRAH-see-ahs",
                    ),
                    FlashcardResponse(
                        question="What does 'agua' mean?",
                        correct_answer="water",
                        options=["food", "water", "house", "family"],
                        explanation="'Agua' is the Spanish word for water",
                        hint="You drink this when thirsty",
                        example_sentence="Necesito agua, por favor.",
                        pronunciation_guide="AH-gwah",
                    ),
                    FlashcardResponse(
                        question="Which word means 'house' in Spanish?",
                        correct_answer="casa",
                        options=["carro", "casa", "comida", "escuela"],
                        explanation="'Casa' refers to a house or home in Spanish",
                        hint="It's where you live",
                        example_sentence="Mi casa es grande.",
                        pronunciation_guide="KAH-sah",
                    ),
                ],
                ExerciseType.TRANSLATION: [
                    FlashcardResponse(
                        question="Translate: 'Good morning'",
                        correct_answer="Buenos días",
                        explanation="This is the standard morning greeting in Spanish",
                        hint="'Buenos' means good, 'días' means days",
                        example_sentence="Buenos días, señora García.",
                        pronunciation_guide="BWAY-nohs DEE-ahs",
                    ),
                    FlashcardResponse(
                        question="Translate: 'My name is...'",
                        correct_answer="Me llamo...",
                        explanation="This is how you introduce yourself in Spanish",
                        hint="'Me llamo' literally means 'I call myself'",
                        example_sentence="Me llamo María.",
                        pronunciation_guide="meh YAH-moh",
                    ),
                    FlashcardResponse(
                        question="Translate: 'I am hungry'",
                        correct_answer="Tengo hambre",
                        explanation="In Spanish, you 'have hunger' rather than 'are hungry'",
                        hint="Use 'tengo' (I have) + 'hambre' (hunger)",
                        example_sentence="Tengo hambre. ¿Hay comida?",
                        pronunciation_guide="TEHN-goh AHM-breh",
                    ),
                    FlashcardResponse(
                        question="Translate: 'Where is the bathroom?'",
                        correct_answer="¿Dónde está el baño?",
                        explanation="A very useful phrase for travelers",
                        hint="'Dónde' = where, 'está' = is, 'baño' = bathroom",
                        example_sentence="Disculpe, ¿dónde está el baño?",
                        pronunciation_guide="DOHN-deh ehs-TAH ehl BAH-nyoh",
                    ),
                    FlashcardResponse(
                        question="Translate: 'How much does it cost?'",
                        correct_answer="¿Cuánto cuesta?",
                        explanation="Essential phrase for shopping and buying things",
                        hint="'Cuánto' = how much, 'cuesta' = it costs",
                        example_sentence="¿Cuánto cuesta este libro?",
                        pronunciation_guide="KWAN-toh KWEH-stah",
                    ),
                ],
            }
        }

        lang_lessons = demo_lessons.get(
            request.target_language.lower(), demo_lessons["spanish"]
        )
        exercise_lessons = lang_lessons.get(
            request.exercise_type, list(lang_lessons.values())[0]
        )

        # Return the requested number of flashcards, cycling through if necessary
        result = []
        for i in range(lesson_length):
            result.append(exercise_lessons[i % len(exercise_lessons)])

        return result

    def _generate_fallback_flashcard(
        self, request: FlashcardRequest
    ) -> FlashcardResponse:
        """Generate basic fallback flashcard when LLM fails"""
        return FlashcardResponse(
            question=f"Practice {request.target_language} - {request.exercise_type.value}",
            correct_answer="Sample answer",
            explanation="This is a fallback exercise",
            hint="Try your best!",
        )

    def _generate_fallback_lesson(
        self, request: FlashcardRequest, lesson_length: int
    ) -> List[FlashcardResponse]:
        """Generate basic fallback lesson when everything else fails"""
        flashcards = []
        for i in range(lesson_length):
            flashcards.append(
                FlashcardResponse(
                    question=f"Practice {request.target_language} - Question {i+1}",
                    correct_answer=f"Sample answer {i+1}",
                    explanation="This is a fallback exercise",
                    hint="Try your best!",
                    example_sentence=f"Example sentence {i+1}",
                )
            )
        return flashcards

    def _simple_grade(self, correct: str, user: str) -> Dict:
        """Simple grading fallback"""
        correct_clean = correct.lower().strip()
        user_clean = user.lower().strip()

        if correct_clean == user_clean:
            return {
                "score": 100,
                "is_correct": True,
                "feedback": "Perfect! Correct answer.",
                "suggestions": [],
            }
        elif user_clean in correct_clean or correct_clean in user_clean:
            return {
                "score": 75,
                "is_correct": True,
                "feedback": "Good! Very close to the correct answer.",
                "suggestions": ["Check for exact spelling and punctuation"],
            }
        else:
            return {
                "score": 0,
                "is_correct": False,
                "feedback": f"Not quite right. The correct answer is: {correct}",
                "suggestions": ["Review the vocabulary", "Practice more examples"],
            }


# Vocabulary and grammar data for different languages
LANGUAGE_DATA = {
    "spanish": {
        "beginner_vocab": [
            "hola",
            "adiós",
            "gracias",
            "por favor",
            "sí",
            "no",
            "agua",
            "comida",
            "casa",
            "familia",
        ],
        "intermediate_vocab": [
            "trabajar",
            "estudiar",
            "importante",
            "diferente",
            "problema",
            "solución",
            "experiencia",
        ],
        "advanced_vocab": [
            "desarrollar",
            "establecer",
            "implementar",
            "específicamente",
            "considerablemente",
        ],
        "grammar_topics": {
            "beginner": ["present tense", "articles", "gender", "plurals"],
            "intermediate": [
                "past tense",
                "subjunctive",
                "ser vs estar",
                "por vs para",
            ],
            "advanced": [
                "conditional",
                "imperative",
                "complex sentences",
                "idiomatic expressions",
            ],
        },
    },
    "french": {
        "beginner_vocab": [
            "bonjour",
            "au revoir",
            "merci",
            "s'il vous plaît",
            "oui",
            "non",
            "eau",
            "nourriture",
        ],
        "intermediate_vocab": [
            "travailler",
            "étudier",
            "important",
            "différent",
            "problème",
            "solution",
        ],
        "advanced_vocab": [
            "développer",
            "établir",
            "implémenter",
            "spécifiquement",
            "considérablement",
        ],
        "grammar_topics": {
            "beginner": ["present tense", "articles", "gender", "plurals"],
            "intermediate": ["past tense", "subjunctive", "irregular verbs"],
            "advanced": ["conditional", "subjunctive", "complex grammar"],
        },
    },
}


def get_vocabulary_for_level(language: str, difficulty: DifficultyLevel) -> List[str]:
    """Get vocabulary words for a specific language and difficulty level"""
    lang_data = LANGUAGE_DATA.get(language.lower(), LANGUAGE_DATA["spanish"])
    vocab_key = f"{difficulty.value}_vocab"
    return lang_data.get(vocab_key, lang_data["beginner_vocab"])


def get_grammar_topics(language: str, difficulty: DifficultyLevel) -> List[str]:
    """Get grammar topics for a specific language and difficulty level"""
    lang_data = LANGUAGE_DATA.get(language.lower(), LANGUAGE_DATA["spanish"])
    return lang_data.get("grammar_topics", {}).get(difficulty.value, ["basic grammar"])

from django.core.management.base import BaseCommand
from lessons.models import Lesson, Vocabulary
from gamification.models import Badge


class Command(BaseCommand):
    help = "Populate the database with sample lesson data"

    def handle(self, *args, **options):
        self.stdout.write("Creating sample lesson data...")

        # Create Spanish lessons
        spanish_lessons = [
            {
                "title": "Basic Greetings",
                "description": "Learn essential Spanish greetings and polite expressions",
                "order": 1,
                "vocabularies": [
                    ("<PERSON>la", "Hello", "<PERSON><PERSON>, ¿cómo estás?"),
                    ("Buenos días", "Good morning", "Buenos días, señora <PERSON>."),
                    ("Buenas tardes", "Good afternoon", "Buenas tardes, ¿cómo está usted?"),
                    ("Buenas noches", "Good evening", "Buenas noches, hasta mañana."),
                    ("Adi<PERSON>", "Goodbye", "Adiós, nos vemos pronto."),
                    ("<PERSON><PERSON><PERSON>", "Thank you", "Gracias por su ayuda."),
                    ("Por favor", "Please", "¿Puede ayudarme, por favor?"),
                    ("De nada", "You're welcome", "De nada, fue un placer ayudar."),
                ]
            },
            {
                "title": "Family and Relationships",
                "description": "Vocabulary for talking about family members and relationships",
                "order": 2,
                "vocabularies": [
                    ("Familia", "Family", "Mi familia es muy grande."),
                    ("Padre", "Father", "Mi padre trabaja en el hospital."),
                    ("Madre", "Mother", "Mi madre cocina muy bien."),
                    ("Hermano", "Brother", "Mi hermano menor estudia música."),
                    ("Hermana", "Sister", "Mi hermana vive en Madrid."),
                    ("Hijo", "Son", "Mi hijo tiene cinco años."),
                    ("Hija", "Daughter", "Mi hija estudia medicina."),
                    ("Abuelo", "Grandfather", "Mi abuelo cuenta historias increíbles."),
                ]
            },
            {
                "title": "Food and Dining",
                "description": "Essential vocabulary for restaurants, food, and cooking",
                "order": 3,
                "vocabularies": [
                    ("Comida", "Food", "La comida española es deliciosa."),
                    ("Restaurante", "Restaurant", "Vamos al restaurante nuevo."),
                    ("Desayuno", "Breakfast", "El desayuno incluye café y tostadas."),
                    ("Almuerzo", "Lunch", "¿Qué quieres para el almuerzo?"),
                    ("Cena", "Dinner", "La cena está lista."),
                    ("Agua", "Water", "¿Puedo tener un vaso de agua?"),
                    ("Pan", "Bread", "El pan está muy fresco."),
                    ("Carne", "Meat", "No como carne, soy vegetariano."),
                ]
            },
            {
                "title": "Numbers and Time",
                "description": "Learn numbers, dates, and time expressions in Spanish",
                "order": 4,
                "vocabularies": [
                    ("Uno", "One", "Tengo uno hermano."),
                    ("Dos", "Two", "Son las dos de la tarde."),
                    ("Tres", "Three", "Necesito tres minutos más."),
                    ("Hora", "Hour/Time", "¿Qué hora es?"),
                    ("Minuto", "Minute", "Llego en cinco minutos."),
                    ("Día", "Day", "Hoy es un día hermoso."),
                    ("Semana", "Week", "La próxima semana tengo vacaciones."),
                    ("Mes", "Month", "Mi cumpleaños es el próximo mes."),
                ]
            },
            {
                "title": "Transportation and Travel",
                "description": "Vocabulary for getting around and traveling",
                "order": 5,
                "vocabularies": [
                    ("Coche", "Car", "Mi coche es azul."),
                    ("Autobús", "Bus", "El autobús llega a las tres."),
                    ("Tren", "Train", "El tren a Barcelona sale mañana."),
                    ("Avión", "Airplane", "Voy a París en avión."),
                    ("Estación", "Station", "La estación está cerca."),
                    ("Aeropuerto", "Airport", "¿Cómo llego al aeropuerto?"),
                    ("Hotel", "Hotel", "Reservé una habitación en el hotel."),
                    ("Viaje", "Trip", "Mi viaje fue increíble."),
                ]
            }
        ]

        # Create lessons and vocabulary
        for lesson_data in spanish_lessons:
            lesson, created = Lesson.objects.get_or_create(
                title=lesson_data["title"],
                defaults={
                    "description": lesson_data["description"],
                    "order": lesson_data["order"],
                    "is_active": True
                }
            )
            
            if created:
                self.stdout.write(f"✓ Created lesson: {lesson.title}")
                
                # Add vocabulary for this lesson
                for word, translation, example in lesson_data["vocabularies"]:
                    vocabulary, vocab_created = Vocabulary.objects.get_or_create(
                        lesson=lesson,
                        word=word,
                        defaults={
                            "translation": translation,
                            "example_sentence": example
                        }
                    )
                    if vocab_created:
                        self.stdout.write(f"  - Added vocabulary: {word} ({translation})")
            else:
                self.stdout.write(f"→ Lesson already exists: {lesson.title}")

        # Create badges
        badges_data = [
            {
                "name": "First Steps",
                "description": "Complete your first lesson"
            },
            {
                "name": "Vocabulary Master",
                "description": "Learn 50 new words"
            },
            {
                "name": "Streak Champion",
                "description": "Maintain a 7-day learning streak"
            },
            {
                "name": "Quick Learner",
                "description": "Complete 5 lessons in one day"
            },
            {
                "name": "Conversation Starter",
                "description": "Complete your first conversation practice"
            },
            {
                "name": "Pronunciation Pro",
                "description": "Get perfect pronunciation on 10 words"
            }
        ]

        for badge_data in badges_data:
            badge, created = Badge.objects.get_or_create(
                name=badge_data["name"],
                defaults={
                    "description": badge_data["description"]
                }
            )
            if created:
                self.stdout.write(f"✓ Created badge: {badge.name}")
            else:
                self.stdout.write(f"→ Badge already exists: {badge.name}")

        self.stdout.write(
            self.style.SUCCESS("Successfully populated sample data!")
        )

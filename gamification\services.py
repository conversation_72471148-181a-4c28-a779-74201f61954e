"""
Gamification Service for TalonTalk C.A.R.E. Framework
Handles automatic XP awarding, achievement unlocking, and progression tracking
"""

import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import transaction
from django.db.models import F, Q
from django.core.cache import cache
from django.dispatch import receiver
from django.db.models.signals import post_save

from gamification.models import Achievement, Badge, Streak, Level
from lessons.models import (
    UserLessonProgress,
    UserContentPerformance,
    UserLearningProfile,
)

logger = logging.getLogger(__name__)


class GamificationService:
    """
    Core gamification service that manages XP, achievements, and progression
    """

    # XP Values for different actions
    XP_VALUES = {
        "flashcard_correct": 5,
        "flashcard_perfect_time": 2,  # Bonus for quick correct answers
        "lesson_complete": 25,
        "lesson_perfect": 10,  # Bonus for 100% accuracy
        "daily_goal_complete": 50,
        "streak_milestone": 20,  # Per day milestone (3, 7, 14, 30 days)
        "care_phase_mastery": 100,  # Mastering a C.A.R.E. phase
        "content_item_mastery": 15,  # Reaching 90%+ proficiency on content
        "challenge_complete": 75,
        "badge_earned": 25,
        "level_up": 100,
    }

    # Achievement criteria templates
    ACHIEVEMENT_CRITERIA = {
        "first_lesson": {"lessons_completed": 1},
        "early_learner": {"lessons_completed": 5},
        "dedicated_student": {"lessons_completed": 20},
        "lesson_master": {"lessons_completed": 50},
        "streak_starter": {"current_streak": 3},
        "streak_keeper": {"current_streak": 7},
        "streak_champion": {"current_streak": 14},
        "streak_legend": {"current_streak": 30},
        "care_contextualizer": {"care_phase_mastery": ["contextualize"]},
        "care_acquirer": {"care_phase_mastery": ["acquire"]},
        "care_reinforcer": {"care_phase_mastery": ["reinforce"]},
        "care_extender": {"care_phase_mastery": ["extend"]},
        "care_master": {
            "care_phase_mastery": ["contextualize", "acquire", "reinforce", "extend"]
        },
        "quick_learner": {"avg_response_time_under": 5.0},
        "accuracy_ace": {"avg_accuracy_over": 0.9},
        "persistent_learner": {"total_sessions": 100},
        "xp_collector": {"total_xp": 1000},
        "xp_master": {"total_xp": 5000},
        "xp_legend": {"total_xp": 10000},
    }

    @classmethod
    def award_xp(
        cls,
        user,
        amount: int,
        source: str,
        source_id: Optional[int] = None,
        description: str = "",
    ) -> Dict:
        """
        Award XP to user and handle level progression
        """
        try:
            with transaction.atomic():
                # Get or create user level
                level, created = Level.objects.get_or_create(user=user)

                # Get or create learning profile
                profile, created = UserLearningProfile.objects.get_or_create(user=user)

                # Award XP
                old_xp = level.xp
                level.xp = F("xp") + amount
                level.save()

                # Refresh from database to get updated XP
                level.refresh_from_db()

                # Update learning profile XP as well
                profile.total_xp = level.xp
                profile.save(update_fields=["total_xp"])

                # Check for level up
                old_level = cls._calculate_level_from_xp(old_xp)
                new_level = cls._calculate_level_from_xp(level.xp)

                level_up_occurred = False
                if new_level > old_level:
                    level.level = new_level
                    level.save(update_fields=["level"])
                    level_up_occurred = True

                    # Award level up bonus XP
                    cls.award_xp(
                        user,
                        cls.XP_VALUES["level_up"],
                        "level_up",
                        new_level,
                        f"Level {new_level} reached!",
                    )

                # Log XP transaction (if we had an XPTransaction model)
                logger.info(
                    f"Awarded {amount} XP to user {user.id} for {source}: {description}"
                )

                # Check for achievements
                new_achievements = cls.check_and_award_achievements(user)

                return {
                    "success": True,
                    "xp_awarded": amount,
                    "total_xp": level.xp,
                    "current_level": level.level,
                    "level_up_occurred": level_up_occurred,
                    "new_achievements": len(new_achievements),
                    "achievement_details": (
                        [
                            {"name": ach.badge.name, "xp_reward": ach.badge.xp_reward}
                            for ach in new_achievements
                        ]
                        if hasattr(Badge, "xp_reward")
                        else []
                    ),
                }

        except Exception as e:
            logger.error(f"Error awarding XP to user {user.id}: {str(e)}")
            return {"success": False, "error": str(e)}

    @classmethod
    def _calculate_level_from_xp(cls, xp: int) -> int:
        """
        Calculate level from XP using exponential curve
        Level 1: 0-99 XP
        Level 2: 100-249 XP
        Level 3: 250-499 XP
        Level 4: 500-999 XP
        etc.
        """
        if xp < 100:
            return 1

        # Exponential leveling: level = floor(log2(xp/50)) + 1
        import math

        level = int(math.log2(xp / 50)) + 1
        return max(1, min(50, level))  # Cap at level 50

    @classmethod
    def update_streak(cls, user) -> Dict:
        """
        Update user's learning streak
        """
        try:
            streak, created = Streak.objects.get_or_create(user=user)
            today = timezone.now().date()

            # Check if user was active yesterday
            yesterday = today - timedelta(days=1)

            if streak.last_active == yesterday:
                # Continue streak
                streak.current_streak += 1
                streak.longest_streak = max(
                    streak.longest_streak, streak.current_streak
                )
            elif streak.last_active == today:
                # Already counted today, no change
                pass
            else:
                # Streak broken, start new
                streak.current_streak = 1

            streak.last_active = today
            streak.save()

            # Update learning profile streak
            profile, created = UserLearningProfile.objects.get_or_create(user=user)
            profile.current_streak = streak.current_streak
            profile.longest_streak = streak.longest_streak
            profile.save(update_fields=["current_streak", "longest_streak"])

            # Award streak milestone XP
            milestone_xp = 0
            if streak.current_streak in [3, 7, 14, 30, 60, 100]:
                milestone_xp = cls.XP_VALUES["streak_milestone"] * streak.current_streak
                cls.award_xp(
                    user,
                    milestone_xp,
                    "streak_milestone",
                    streak.current_streak,
                    f"{streak.current_streak}-day streak milestone!",
                )

            logger.info(
                f"Updated streak for user {user.id}: {streak.current_streak} days"
            )

            return {
                "success": True,
                "current_streak": streak.current_streak,
                "longest_streak": streak.longest_streak,
                "milestone_xp_awarded": milestone_xp,
                "is_milestone": streak.current_streak in [3, 7, 14, 30, 60, 100],
            }

        except Exception as e:
            logger.error(f"Error updating streak for user {user.id}: {str(e)}")
            return {"success": False, "error": str(e)}

    @classmethod
    def check_and_award_achievements(cls, user) -> List[Achievement]:
        """
        Check and award new achievements for user
        """
        try:
            # Get existing achievements
            existing_badges = set(
                Achievement.objects.filter(user=user).values_list(
                    "badge__name", flat=True
                )
            )

            # Get user stats for achievement checking
            stats = cls._get_user_stats(user)

            new_achievements = []

            # Check each achievement criteria
            for achievement_name, criteria in cls.ACHIEVEMENT_CRITERIA.items():
                if achievement_name in existing_badges:
                    continue  # Already earned

                if cls._check_achievement_criteria(stats, criteria):
                    # Create or get badge
                    badge, created = Badge.objects.get_or_create(
                        name=achievement_name.replace("_", " ").title(),
                        defaults={
                            "description": cls._get_achievement_description(
                                achievement_name
                            ),
                            # 'xp_reward': cls._get_achievement_xp_reward(achievement_name)
                        },
                    )

                    # Award achievement
                    achievement = Achievement.objects.create(user=user, badge=badge)
                    new_achievements.append(achievement)

                    # Award badge XP if applicable
                    badge_xp = cls._get_achievement_xp_reward(achievement_name)
                    if badge_xp > 0:
                        cls.award_xp(
                            user,
                            badge_xp,
                            "badge",
                            badge.id,
                            f"Earned badge: {badge.name}",
                        )

                    logger.info(f"Awarded achievement '{badge.name}' to user {user.id}")

            return new_achievements

        except Exception as e:
            logger.error(f"Error checking achievements for user {user.id}: {str(e)}")
            return []

    @classmethod
    def _get_user_stats(cls, user) -> Dict:
        """
        Get comprehensive user statistics for achievement checking
        """
        try:
            level = Level.objects.filter(user=user).first()
            profile = UserLearningProfile.objects.filter(user=user).first()
            streak = Streak.objects.filter(user=user).first()

            # Count completed lessons
            lessons_completed = UserLessonProgress.objects.filter(
                user=user, completed=True
            ).count()

            # Count content performances for C.A.R.E. analysis
            performances = UserContentPerformance.objects.filter(user=user)

            care_phase_masteries = []
            for phase in ["contextualize", "acquire", "reinforce", "extend"]:
                phase_performances = performances.filter(
                    content_item__tags__contains=[phase], proficiency_score__gte=0.9
                )
                if phase_performances.count() >= 5:  # Need at least 5 mastered items
                    care_phase_masteries.append(phase)

            # Calculate average metrics
            if performances.exists():
                avg_accuracy = (
                    performances.aggregate(avg=models.Avg("proficiency_score"))["avg"]
                    or 0.0
                )

                avg_response_time = (
                    performances.exclude(average_response_time__isnull=True).aggregate(
                        avg=models.Avg("average_response_time")
                    )["avg"]
                    or 10.0
                )
            else:
                avg_accuracy = 0.0
                avg_response_time = 10.0

            return {
                "total_xp": level.xp if level else 0,
                "current_level": level.level if level else 1,
                "current_streak": streak.current_streak if streak else 0,
                "longest_streak": streak.longest_streak if streak else 0,
                "lessons_completed": lessons_completed,
                "total_sessions": profile.total_sessions if profile else 0,
                "avg_accuracy": avg_accuracy,
                "avg_response_time": avg_response_time,
                "care_phase_mastery": care_phase_masteries,
            }

        except Exception as e:
            logger.error(f"Error getting user stats for {user.id}: {str(e)}")
            return {}

    @classmethod
    def _check_achievement_criteria(cls, stats: Dict, criteria: Dict) -> bool:
        """
        Check if user stats meet achievement criteria
        """
        for key, required_value in criteria.items():
            user_value = stats.get(key, 0)

            if key.endswith("_over"):
                # Greater than criteria
                base_key = key.replace("_over", "")
                if stats.get(base_key, 0) <= required_value:
                    return False
            elif key.endswith("_under"):
                # Less than criteria
                base_key = key.replace("_under", "")
                if stats.get(base_key, float("inf")) >= required_value:
                    return False
            elif key == "care_phase_mastery":
                # Special handling for C.A.R.E. phase mastery
                user_masteries = set(stats.get("care_phase_mastery", []))
                required_masteries = set(required_value)
                if not required_masteries.issubset(user_masteries):
                    return False
            else:
                # Direct comparison
                if user_value < required_value:
                    return False

        return True

    @classmethod
    def _get_achievement_description(cls, achievement_name: str) -> str:
        """
        Get description for achievement
        """
        descriptions = {
            "first_lesson": "Complete your first lesson",
            "early_learner": "Complete 5 lessons",
            "dedicated_student": "Complete 20 lessons",
            "lesson_master": "Complete 50 lessons",
            "streak_starter": "Maintain a 3-day learning streak",
            "streak_keeper": "Maintain a 7-day learning streak",
            "streak_champion": "Maintain a 14-day learning streak",
            "streak_legend": "Maintain a 30-day learning streak",
            "care_contextualizer": "Master the Contextualize phase of C.A.R.E.",
            "care_acquirer": "Master the Acquire phase of C.A.R.E.",
            "care_reinforcer": "Master the Reinforce phase of C.A.R.E.",
            "care_extender": "Master the Extend phase of C.A.R.E.",
            "care_master": "Master all phases of the C.A.R.E. framework",
            "quick_learner": "Maintain average response time under 5 seconds",
            "accuracy_ace": "Maintain 90%+ accuracy across all content",
            "persistent_learner": "Complete 100 learning sessions",
            "xp_collector": "Earn 1,000 total XP",
            "xp_master": "Earn 5,000 total XP",
            "xp_legend": "Earn 10,000 total XP",
        }

        return descriptions.get(
            achievement_name,
            f'Achievement: {achievement_name.replace("_", " ").title()}',
        )

    @classmethod
    def _get_achievement_xp_reward(cls, achievement_name: str) -> int:
        """
        Get XP reward for achievement
        """
        xp_rewards = {
            "first_lesson": 50,
            "early_learner": 100,
            "dedicated_student": 200,
            "lesson_master": 500,
            "streak_starter": 50,
            "streak_keeper": 100,
            "streak_champion": 250,
            "streak_legend": 500,
            "care_contextualizer": 200,
            "care_acquirer": 200,
            "care_reinforcer": 200,
            "care_extender": 200,
            "care_master": 1000,
            "quick_learner": 150,
            "accuracy_ace": 300,
            "persistent_learner": 500,
            "xp_collector": 100,
            "xp_master": 500,
            "xp_legend": 1000,
        }

        return xp_rewards.get(achievement_name, 25)


# Signal handlers for automatic gamification
@receiver(post_save, sender=UserLessonProgress)
def lesson_completed_handler(sender, instance, created, **kwargs):
    """Handle lesson completion events"""
    if instance.completed:
        user = instance.user

        # Award lesson XP
        xp_amount = GamificationService.XP_VALUES["lesson_complete"]

        # Check for perfect score bonus
        if hasattr(instance, "accuracy") and getattr(instance, "accuracy", 0) >= 1.0:
            xp_amount += GamificationService.XP_VALUES["lesson_perfect"]

        # Award XP
        GamificationService.award_xp(
            user,
            xp_amount,
            "lesson",
            instance.lesson.id,
            f"Completed lesson: {instance.lesson.title}",
        )

        # Update streak
        GamificationService.update_streak(user)

        logger.info(
            f"Processed lesson completion for user {user.id}: {instance.lesson.title}"
        )


@receiver(post_save, sender=UserContentPerformance)
def content_performance_handler(sender, instance, created, **kwargs):
    """Handle content performance updates"""
    user = instance.user

    # Award XP for correct answers
    if instance.times_correct > 0:
        # Calculate XP based on recent performance
        recent_correct = instance.times_correct
        if hasattr(instance, "_previous_correct"):
            recent_correct = instance.times_correct - instance._previous_correct

        if recent_correct > 0:
            xp_per_correct = GamificationService.XP_VALUES["flashcard_correct"]

            # Bonus for quick responses
            if instance.average_response_time and instance.average_response_time <= 5.0:
                xp_per_correct += GamificationService.XP_VALUES[
                    "flashcard_perfect_time"
                ]

            total_xp = xp_per_correct * recent_correct

            GamificationService.award_xp(
                user,
                total_xp,
                "content",
                instance.content_item.id,
                f"Correct answers: {recent_correct}",
            )

    # Check for content mastery
    if instance.proficiency_score >= 0.9 and instance.times_seen >= 3:
        # Award mastery bonus
        GamificationService.award_xp(
            user,
            GamificationService.XP_VALUES["content_item_mastery"],
            "mastery",
            instance.content_item.id,
            f"Mastered: {instance.content_item.question_text[:50]}...",
        )


# Global service instance
gamification_service = GamificationService()
